#
#███╗░░██╗██╗░░░██╗██╗░░██╗██╗████████╗██╗░░██╗░█████╗░██████╗░░█████╗░
#████╗░██║╚██╗░██╔╝╚██╗██╔╝╚═╝╚══██╔══╝██║░░██║██╔══██╗██╔══██╗██╔══██╗
#██╔██╗██║░╚████╔╝░░╚███╔╝░░░░░░░██║░░░███████║███████║██████╔╝███████║
#██║╚████║░░╚██╔╝░░░██╔██╗░░░░░░░██║░░░██╔══██║██╔══██║██╔══██╗██╔══██║
#██║░╚███║░░░██║░░░██╔╝╚██╗░░░░░░██║░░░██║░░██║██║░░██║██║░░██║██║░░██║
#╚═╝░░╚══╝░░░╚═╝░░░╚═╝░░╚═╝░░░░░░╚═╝░░░╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝
#
# VOID SOVEREIGN COMBAT SKILLS - Created by vexy
#

# Basic melee combat with void energy
NYXTHARA_COMBAT:
  Conditions:
  - playerwithin{d=3} true
  Skills:
  - damage{a=6to10;repeat=2;repeatinterval=3} @target
  - throw{velocity=2.5;velocityY=0.8} @target
  - potion{type=WITHER;duration=80;level=1;force=true} @target 0.3
  - potion{type=BLINDNESS;duration=40;level=1;force=true} @target 0.2
  - effect:particles{p=portal;a=15;hS=1;vS=1;s=0.3} @target

# Void Phase - Teleportation and ranged attacks
NYXTHARA_VOID_PHASE:
  Cooldown: 15
  Conditions:
  - stance{stance=NYXTHARA_NORMAL} true
  - playerwithin{d=15} true
  Skills:
  - setstance{stance=NYXTHARA_VOID_PHASE} @self
  - message{m="<mob.name>&f<&co>&r &5*The air crackles with void energy*"} @PlayersInRadius{r=60}
  - effect:particles{p=portal;a=50;hS=3;vS=3;s=0.5} @self
  - sound{s=entity.enderman.teleport;v=0.8;p=0.6} @self
  - teleport{l=@target{limit=1}} @self
  - delay 5
  - projectile{onTick=NYXTHARA_VOID_PROJECTILE-Tick;onHit=NYXTHARA_VOID_PROJECTILE-Hit;onEnd=NYXTHARA_VOID_PROJECTILE-End;v=15;i=1;hR=1.5;vR=1.5;tyo=0.8;sfo=0;sso=3;eso=0} @target
  - delay 8
  - projectile{onTick=NYXTHARA_VOID_PROJECTILE-Tick;onHit=NYXTHARA_VOID_PROJECTILE-Hit;onEnd=NYXTHARA_VOID_PROJECTILE-End;v=15;i=1;hR=1.5;vR=1.5;tyo=0.8;sfo=0;sso=-3;eso=0} @target
  - delay 8
  - projectile{onTick=NYXTHARA_VOID_PROJECTILE-Tick;onHit=NYXTHARA_VOID_PROJECTILE-Hit;onEnd=NYXTHARA_VOID_PROJECTILE-End;v=15;i=1;hR=1.5;vR=1.5;tyo=1.8;sfo=0;sso=3;eso=0;g=0.15;hn=8} @target
  - delay 12
  - projectile{onTick=NYXTHARA_VOID_PROJECTILE-Tick;onHit=NYXTHARA_VOID_PROJECTILE-Hit;onEnd=NYXTHARA_VOID_PROJECTILE-End;v=15;i=1;hR=1.5;vR=1.5;tyo=1.8;sfo=0;sso=-3;eso=0;g=0.15;hn=8} @target
  - delay 15
  - setstance{stance=NYXTHARA_NORMAL} @self

# Shadow Step - Quick teleportation behind target
NYXTHARA_SHADOW_STEP:
  Cooldown: 12
  Conditions:
  - playerwithin{d=20} true
  - playerwithin{d=4} false
  Skills:
  - message{m="<mob.name>&f<&co>&r &5*Shadows coalesce*"} @PlayersInRadius{r=40}
  - effect:particles{p=smoke_large;a=30;hS=2;vS=2;s=0.4} @self
  - sound{s=entity.enderman.teleport;v=0.6;p=0.8} @self
  - teleport{l=@target{limit=1};yoffset=0;xoffset=-3;zoffset=-3} @self
  - delay 3
  - effect:particles{p=portal;a=25;hS=1.5;vS=1.5;s=0.3} @self
  - damage{a=8to12} @target
  - potion{type=SLOWNESS;duration=60;level=2;force=true} @target

# Void Aura - Passive area damage
NYXTHARA_VOID_AURA:
  Conditions:
  - stance{stance=NYXTHARA_NORMAL} true
  Skills:
  - effect:particles{p=portal;a=20;hS=4;vS=1;s=0.2} @self
  - damage{a=2to4} @PlayersInRadius{r=5}
  - potion{type=HUNGER;duration=100;level=1;force=true} @PlayersInRadius{r=5} 0.3

# Dimensional Rift - Creates void rifts that damage players
NYXTHARA_DIMENSIONAL_RIFT:
  Cooldown: 20
  Conditions:
  - playerwithin{d=25} true
  Skills:
  - message{m="<mob.name>&f<&co>&r &5*Reality tears open*"} @PlayersInRadius{r=60}
  - sound{s=entity.wither.spawn;v=0.5;p=0.4} @self
  - skill{s=NYXTHARA_CREATE_RIFT} @RandomLocationsNearTargets{a=3;r=8;minr=3}
  - delay 40
  - skill{s=NYXTHARA_CREATE_RIFT} @RandomLocationsNearTargets{a=2;r=12;minr=5}
  - delay 60
  - skill{s=NYXTHARA_CREATE_RIFT} @RandomLocationsNearTargets{a=4;r=6;minr=2}

NYXTHARA_CREATE_RIFT:
  Skills:
  - effect:particles{p=portal;a=40;hS=2;vS=3;s=0.6} @origin
  - sound{s=block.portal.ambient;v=0.4;p=0.6} @origin
  - delay 20
  - damage{a=5to8} @PlayersInRadius{r=3;origin=@origin}
  - potion{type=LEVITATION;duration=40;level=1;force=true} @PlayersInRadius{r=3;origin=@origin} 0.4
  - effect:particles{p=explosion_large;a=1;hS=0;vS=0;s=0} @origin

# Enhanced damage reaction
NYXTHARA_EFFECT_DAMAGED:
  Cooldown: 3
  Skills:
  - effect:particles{p=smoke_large;a=15;hS=1;vS=1;s=0.3} @self
  - sound{s=entity.enderman.hurt;v=0.4;p=0.7} @self
  - teleport{l=@target{limit=1};yoffset=0;xoffset=5;zoffset=5} @self 0.3

# Projectile effects for void attacks
NYXTHARA_VOID_PROJECTILE-Tick:
  Skills:
  - effect:particles{p=portal;a=3;hS=0.5;vS=0.5;s=0.1} @origin

NYXTHARA_VOID_PROJECTILE-Hit:
  Skills:
  - damage{a=6to9} @target
  - potion{type=WITHER;duration=60;level=1;force=true} @target
  - effect:particles{p=portal;a=20;hS=1;vS=1;s=0.4} @target
  - sound{s=entity.generic.explode;v=0.3;p=1.2} @target

NYXTHARA_VOID_PROJECTILE-End:
  Skills:
  - effect:particles{p=portal;a=15;hS=1;vS=1;s=0.3} @origin
#
#
#
#
#
#
#
#
#
#
# 𝑪𝑹𝑬𝑨𝑻𝑬𝑫 𝑩𝒀 𝑽𝑬𝑿𝒀
# 𝑩𝑨𝑺𝑬𝑫 𝑶𝑵 𝑻𝑯𝑨𝑹𝑲𝑶𝑵 𝑩𝒀 𝑨𝑳𝑻𝑬𝑹𝑵𝑨𝑻𝑰𝑽𝑬𝑺𝑶𝑨𝑷
