# <PERSON><PERSON>'thara, the Void Sovereign
## Created by vexy | Based on Tharkon by AlternativeSoap

### 🌟 Overview
**N<PERSON>'thara, the Void Sovereign** is an enhanced boss variant inspired by the original Ragnis Tharkon. This shadow/void-themed boss features unique mechanics, impressive special abilities, and exclusive rewards that make for an epic encounter.

### ⚔️ Boss Features

#### **Core Identity**
- **Name**: <PERSON><PERSON>'thara, the Void Sovereign
- **Theme**: Shadow/Void/Dimensional magic
- **Mob Type**: Wither Skeleton (enhanced with custom abilities)
- **Faction**: VOID_SOVEREIGN
- **Boss Bar**: Purple with segmented style and atmospheric effects

#### **Enhanced Mechanics**
- **Dynamic Health Scaling**: 500-3000 HP based on player count (1-10+ players)
- **Multi-Phase Combat**: Normal → Void Phase → Rage Mode
- **Teleportation Abilities**: Shadow Step, Dimensional Shift
- **Area Control**: Void Rifts, Reality Tears, Dimensional Collapse

#### **Special Abilities**
1. **Void Rage** (Ultimate - <20% HP)
   - Enters primal rage state with enhanced abilities
   - Unleashes Void Storm, Dimensional Collapse, Void Nova, Reality Tear, and Void Apocalypse
   - Increased speed and damage resistance

2. **Shadow Clone** (<50% HP)
   - Creates 2-3 shadow duplicates with reduced health
   - Clones have teleportation and combat abilities
   - Temporary summons that despawn after 10 minutes

3. **Dimensional Rift**
   - Creates void zones that damage and levitate players
   - Multiple rifts spawn in waves around the battlefield
   - Combines area denial with spectacular visual effects

4. **Void Storm**
   - Massive area attack with void lightning strikes
   - Multiple waves of lightning at random locations
   - High damage with wither effects

5. **Reality Manipulation**
   - Teleports players randomly during Dimensional Collapse
   - Warps reality with confusion and slowness effects
   - Creates unstable void zones

### 🎁 Exclusive Rewards

#### **Legendary Artifacts**
- **Crown of the Void Sovereign** - Ultimate helmet with max enchantments
- **Shadowrend, Blade of the Void** - Legendary sword with enhanced damage
- **Cloak of Endless Night** - Legendary chestplate with movement bonus
- **Boots of Shadow Walking** - Enhanced boots with multiple movement enchantments

#### **Mystical Items**
- **Void Crystal** - Rare crafting material
- **Dimensional Shard** - Reality fragments for void crafting
- **Essence of Nyx'thara** - Boss essence containing her power
- **Staff of Void Mastery** - Magical staff for dark magic
- **Key to the Void Realm** - Dimensional gateway key
- **Orb of Infinite Darkness** - Mystical orb with otherworldly visions

#### **Enhanced Drop Tables**
- Higher-tier materials and enchanted books
- Void-themed items with unique properties
- Increased experience rewards (75-125 levels)
- Multiple drop categories for varied rewards

### 🎮 Testing Guide

#### **Spawning the Boss**
```
/mm mobs spawn NYXTHARA [amount] [world] [x] [y] [z]
```

#### **Recommended Testing Setup**
1. **Arena Size**: Minimum 50x50 blocks open area
2. **Player Count**: Test with 1, 3, 5, and 8+ players for scaling
3. **Equipment**: Full netherite gear recommended for survival testing
4. **Potions**: Healing, regeneration, and fire resistance recommended

#### **Testing Checklist**
- [ ] Boss spawns correctly with proper display name and boss bar
- [ ] Health scales appropriately with player count
- [ ] All combat phases trigger at correct health percentages
- [ ] Shadow clones spawn and function properly
- [ ] Void rifts create damage zones correctly
- [ ] Teleportation abilities work without errors
- [ ] Drop tables function and items have correct properties
- [ ] Victory messages and effects display properly
- [ ] Boss despawns correctly after death

#### **Performance Testing**
- Monitor server TPS during special abilities
- Test with maximum player count (10+)
- Verify particle effects don't cause lag
- Check memory usage during extended fights

### 🔧 Configuration Notes

#### **Damage Modifiers**
- Reduced physical damage (0.7x)
- Increased fire damage (1.5x) - weakness to light/fire
- Immune to void, wither, and poison
- Vulnerable to magic attacks (0.5x)

#### **AI Behavior**
- Custom AI with cleared vanilla behaviors
- 100-block follow range for epic encounters
- 80-block max combat distance
- Threat table enabled for proper targeting

#### **Balance Considerations**
- Boss is designed for group encounters (3-8 players optimal)
- Solo players will face significant challenge
- Rewards scale with difficulty and player participation
- 70% damage requirement for drops (vs 75% for Tharkon)

### 🐛 Known Issues & Troubleshooting

#### **Common Issues**
1. **Boss not spawning**: Check MythicMobs version compatibility
2. **Missing particles**: Verify client particle settings
3. **Drop issues**: Ensure custom items are properly registered
4. **Performance lag**: Reduce particle amounts in skills if needed

#### **Compatibility**
- Requires MythicMobs 5.0+
- Compatible with most popular plugins
- Tested on Paper/Spigot 1.19+
- May require adjustment for older versions

### 📁 File Structure
```
Boss Ragnis Tharkon/
├── Mobs/
│   ├── NYXTHARA.yml (Main boss configuration)
│   └── NYXTHARA_MINIONS.yml (Shadow clones and void wraiths)
├── Skills/
│   ├── NYXTHARA_COMBAT.yml (Core combat abilities)
│   ├── NYXTHARA_SPECIAL_ABILITIES.yml (Ultimate abilities)
│   └── NYXTHARA_SUPPORT_SKILLS.yml (Support and utility skills)
├── DropTables/
│   └── NYXTHARA_DROPTABLES.yml (All drop configurations)
├── Items/
│   └── NYXTHARA_ITEMS.yml (Custom legendary items)
└── packinfo.yml (Updated with vexy attribution)
```

### 🎨 Visual Theme
- **Color Scheme**: Purple gradients (#8a2be2 to #4b0082 to #2e0054)
- **Particles**: Portal, smoke, and explosion effects
- **Sounds**: Wither, enderman, and ender dragon audio
- **Atmosphere**: Dark, mystical, otherworldly

### 🏆 Credits
- **Original Concept**: AlternativeSoap (Ragnis Tharkon)
- **Void Sovereign Variant**: vexy
- **Enhanced Mechanics**: Custom void/shadow theme implementation
- **Special Thanks**: MythicMobs community for inspiration

---

*"In the depths of the void, where light fears to tread, Nyx'thara reigns supreme over shadow and darkness. Challenge her domain at your own peril, for the Void Sovereign shows no mercy to those who dare disturb her eternal slumber."*

### 🚀 Future Enhancements
- Additional minion types (Void Wraiths, Shadow Sentinels)
- Custom arena with void-themed decorations
- Integration with custom dungeon systems
- Seasonal variants with different abilities
- Achievement system for boss encounters
