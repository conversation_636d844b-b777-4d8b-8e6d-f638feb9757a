#
#████████╗██╗░░██╗░█████╗░██████╗░██╗░░██╗░█████╗░███╗░░██╗  ██████╗░░█████╗░░██████╗░██████╗
#╚══██╔══╝██║░░██║██╔══██╗██╔══██╗██║░██╔╝██╔══██╗████╗░██║  ██╔══██╗██╔══██╗██╔════╝██╔════╝
#░░░██║░░░███████║███████║██████╔╝█████═╝░██║░░██║██╔██╗██║  ██████╦╝██║░░██║╚█████╗░╚█████╗░
#░░░██║░░░██╔══██║██╔══██║██╔══██╗██╔═██╗░██║░░██║██║╚████║  ██╔══██╗██║░░██║░╚═══██╗░╚═══██╗
#░░░██║░░░██║░░██║██║░░██║██║░░██║██║░╚██╗╚█████╔╝██║░╚███║  ██████╦╝╚█████╔╝██████╔╝██████╔╝
#░░░╚═╝░░░╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝░╚════╝░╚═╝░░╚══╝  ╚═════╝░░╚════╝░╚═════╝░╚═════╝░
#
THARKON_MINION:
  MobType: MAGMA_CUBE
  Health: 15
  Damage: 4
  Display: '<gradient:#ff0009:#8a5153>&lIgnis</gradient>'
  Modules:
    ThreatTable: true
  Faction: THARKON
  Options:
    Size: 0to2
    AlwaysShowName: false
    FollowRange: 50
    Despawn: false
    PreventRenaming: true
    PreventSunburn: true
    PreventOtherDrops: true
    PreventSlimeSplit: true
  KillMessages:
  - '&f<target.name> &7was slain by <caster.display>'
  Drops:
  - exp 5to20 1
  Skills:
  - remove{delay=1200} @self ~onSpawn
#
  - lunge{velocity=1.4;velocityY=0.5} @target ~onTimer:100 1
  - skill{s=IGNIS_EXPLODE_SIZE_1} ~onTimer:5 1
  - skill{s=IGNIS_EXPLODE_SIZE_1} ~onAttack 1
  - skill{s=IGNIS_EXPLODE_SIZE_2} ~onTimer:5 1
  - skill{s=IGNIS_EXPLODE_SIZE_2} ~onAttack 1

THARKON_DOPPELGANGER:
  MobType: BLAZE
  Health: 20
  Damage: 4
  Display: '<gradient:#ff0022:#e600ff>&lRagnis Tharkon</gradient>'
  Modules:
    ThreatTable: true
  Faction: THARKON
  AIGoalSelectors:
  - clear
  AITargetSelectors:
  - clear
  Options:
    AlwaysShowName: true
    FollowRange: 50
    Despawn: false
    PreventRenaming: true
    PreventSunburn: true
    PreventOtherDrops: true
    PreventMobKillDrops: true
  KillMessages:
  - '&f<target.name> &7was slain by <caster.display>'
  Drops:
  - exp 20to30 1
  Skills:
  - remove{delay=600} @self ~onSpawn
#
  - skill{s=THARKON_DOPPELGANGER_ONSPAWN_AI} ~onSpawn 1
  - skill{s=THARKON_DOPPELGANGER_COMBAT} ~onAttack 1
  - skill{s=THARKON_DOPPELGANGER_COMBAT_FLYING} ~onTimer:100 0.8
  - skill{s=THARKON_COMBAT_LUNGE} ~onTimer:100 1
  - ignite{ticks=100} @target ~onAttack 0.5
#
#
#
#
#
#
#
#
#
#
# 𝑴𝑨𝑫𝑬 𝑩𝒀 𝑨𝑳𝑻𝑬𝑹𝑵𝑨𝑻𝑰𝑽𝑬𝑺𝑶𝑨𝑷
# 𝑫𝑰𝑺𝑪𝑶𝑹𝑫: https://discord.gg/eUFRvyzJua