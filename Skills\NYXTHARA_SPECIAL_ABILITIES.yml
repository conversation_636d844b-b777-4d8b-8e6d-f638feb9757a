#
#███╗░░██╗██╗░░░██╗██╗░░██╗██╗████████╗██╗░░██╗░█████╗░██████╗░░█████╗░
#████╗░██║╚██╗░██╔╝╚██╗██╔╝╚═╝╚══██╔══╝██║░░██║██╔══██╗██╔══██╗██╔══██╗
#██╔██╗██║░╚████╔╝░░╚███╔╝░░░░░░░██║░░░███████║███████║██████╔╝███████║
#██║╚████║░░╚██╔╝░░░██╔██╗░░░░░░░██║░░░██╔══██║██╔══██║██╔══██╗██╔══██║
#██║░╚███║░░░██║░░░██╔╝╚██╗░░░░░░██║░░░██║░░██║██║░░██║██║░░██║██║░░██║
#╚═╝░░╚══╝░░░╚═╝░░░╚═╝░░╚═╝░░░░░░╚═╝░░░╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝
#
# VOID SOVEREIGN SPECIAL ABILITIES - Created by vexy
#

# VOID RAGE - Ultimate ability when health is low
NYXTHARA_VOID_RAGE:
  Cooldown: 80
  Skills:
  - delay 1
  - setstance{stance=NYXTHARA_VOID_RAGE} @self
  - skill{s=NYXTHARA_RAGE_RANDOMMESSAGE} @self 1
  - message{m="&5&l⚡ THE VOID SOVEREIGN ENTERS A PRIMAL RAGE! ⚡"} @PlayersInRadius{r=80}
  - sound{s=entity.wither.spawn;v=1.0;p=0.3} @PlayersInRadius{r=80}
  - effect:particles{p=portal;a=100;hS=5;vS=5;s=1.0} @self
  - potion{type=SPEED;duration=600;level=2;force=true} @self
  - potion{type=DAMAGE_RESISTANCE;duration=600;level=1;force=true} @self
  - delay 60
  - skill{s=NYXTHARA_VOID_STORM} @self 0.9
  - skill{s=NYXTHARA_SHADOW_BARRAGE} @self 0.7
  - delay 120
  - skill{s=NYXTHARA_DIMENSIONAL_COLLAPSE} @self 0.8
  - skill{s=NYXTHARA_SHADOW_BARRAGE} @self 0.7
  - delay 120
  - skill{s=NYXTHARA_VOID_NOVA} @self 0.9
  - skill{s=NYXTHARA_SHADOW_BARRAGE} @self 0.7
  - delay 120
  - skill{s=NYXTHARA_REALITY_TEAR} @self 0.8
  - skill{s=NYXTHARA_SHADOW_BARRAGE} @self 0.7
  - delay 120
  - skill{s=NYXTHARA_VOID_APOCALYPSE} @self 0.9
  - setstance{stance=NYXTHARA_NORMAL} @self

# SHADOW CLONE - Creates shadow duplicates
NYXTHARA_SHADOW_CLONE:
  Cooldown: 25
  Skills:
  - message{m="<mob.name>&f<&co>&r &5*Shadows split and multiply*"} @PlayersInRadius{r=60}
  - sound{s=entity.enderman.teleport;v=0.8;p=0.4} @self
  - effect:particles{p=smoke_large;a=50;hS=3;vS=3;s=0.6} @self
  - summon{type=NYXTHARA_SHADOW_CLONE;amount=2;radius=5} @self
  - delay 40
  - summon{type=NYXTHARA_SHADOW_CLONE;amount=1;radius=8} @self

# VOID STORM - Massive area attack
NYXTHARA_VOID_STORM:
  Skills:
  - message{m="&5&l⚡ VOID STORM UNLEASHED! ⚡"} @PlayersInRadius{r=80}
  - sound{s=entity.lightning_bolt.thunder;v=0.8;p=0.6} @PlayersInRadius{r=80}
  - effect:particles{p=portal;a=200;hS=10;vS=8;s=1.5} @self
  - delay 20
  - skill{s=NYXTHARA_VOID_LIGHTNING} @RandomLocationsNearTargets{a=8;r=15;minr=3}
  - delay 15
  - skill{s=NYXTHARA_VOID_LIGHTNING} @RandomLocationsNearTargets{a=6;r=20;minr=5}
  - delay 15
  - skill{s=NYXTHARA_VOID_LIGHTNING} @RandomLocationsNearTargets{a=10;r=12;minr=2}

NYXTHARA_VOID_LIGHTNING:
  Skills:
  - effect:particles{p=portal;a=30;hS=1;vS=8;s=0.8} @origin
  - sound{s=entity.lightning_bolt.impact;v=0.6;p=1.2} @origin
  - delay 10
  - damage{a=8to12} @PlayersInRadius{r=4;origin=@origin}
  - potion{type=WITHER;duration=100;level=2;force=true} @PlayersInRadius{r=4;origin=@origin}
  - effect:particles{p=explosion_large;a=2;hS=0;vS=0;s=0} @origin

# SHADOW BARRAGE - Rapid projectile attacks
NYXTHARA_SHADOW_BARRAGE:
  Skills:
  - message{m="<mob.name>&f<&co>&r &5*Darkness coalesces into deadly projectiles*"} @PlayersInRadius{r=60}
  - projectile{onTick=NYXTHARA_SHADOW_PROJECTILE-Tick;onHit=NYXTHARA_SHADOW_PROJECTILE-Hit;onEnd=NYXTHARA_SHADOW_PROJECTILE-End;v=18;i=1;hR=1;vR=1;tyo=0.5;sfo=0;sso=4;eso=0} @target
  - delay 5
  - projectile{onTick=NYXTHARA_SHADOW_PROJECTILE-Tick;onHit=NYXTHARA_SHADOW_PROJECTILE-Hit;onEnd=NYXTHARA_SHADOW_PROJECTILE-End;v=18;i=1;hR=1;vR=1;tyo=0.5;sfo=0;sso=-4;eso=0} @target
  - delay 5
  - projectile{onTick=NYXTHARA_SHADOW_PROJECTILE-Tick;onHit=NYXTHARA_SHADOW_PROJECTILE-Hit;onEnd=NYXTHARA_SHADOW_PROJECTILE-End;v=18;i=1;hR=1;vR=1;tyo=1.5;sfo=0;sso=4;eso=0;g=0.1;hn=12} @target
  - delay 5
  - projectile{onTick=NYXTHARA_SHADOW_PROJECTILE-Tick;onHit=NYXTHARA_SHADOW_PROJECTILE-Hit;onEnd=NYXTHARA_SHADOW_PROJECTILE-End;v=18;i=1;hR=1;vR=1;tyo=1.5;sfo=0;sso=-4;eso=0;g=0.1;hn=12} @target
  - delay 5
  - projectile{onTick=NYXTHARA_SHADOW_PROJECTILE-Tick;onHit=NYXTHARA_SHADOW_PROJECTILE-Hit;onEnd=NYXTHARA_SHADOW_PROJECTILE-End;v=18;i=1;hR=1;vR=1;tyo=0.5;sfo=0;sso=0;eso=0} @target

# DIMENSIONAL COLLAPSE - Reality warping attack
NYXTHARA_DIMENSIONAL_COLLAPSE:
  Skills:
  - message{m="&5&l⚡ REALITY ITSELF BENDS TO MY WILL! ⚡"} @PlayersInRadius{r=80}
  - sound{s=entity.wither.spawn;v=0.8;p=0.2} @PlayersInRadius{r=80}
  - effect:particles{p=portal;a=150;hS=8;vS=8;s=1.2} @self
  - delay 30
  - teleport{l=@PlayersInRadius{r=30;limit=1}} @PlayersInRadius{r=30}
  - damage{a=10to15} @PlayersInRadius{r=30}
  - potion{type=CONFUSION;duration=200;level=2;force=true} @PlayersInRadius{r=30}
  - potion{type=SLOWNESS;duration=150;level=3;force=true} @PlayersInRadius{r=30}

# VOID NOVA - Explosive void energy
NYXTHARA_VOID_NOVA:
  Skills:
  - message{m="&5&l⚡ VOID NOVA - EMBRACE THE DARKNESS! ⚡"} @PlayersInRadius{r=80}
  - sound{s=entity.generic.explode;v=1.0;p=0.4} @PlayersInRadius{r=80}
  - effect:particles{p=portal;a=300;hS=12;vS=12;s=2.0} @self
  - delay 40
  - damage{a=12to18} @PlayersInRadius{r=15}
  - damage{a=8to12} @PlayersInRadius{r=25}
  - damage{a=4to8} @PlayersInRadius{r=35}
  - potion{type=WITHER;duration=200;level=3;force=true} @PlayersInRadius{r=15}
  - potion{type=WITHER;duration=150;level=2;force=true} @PlayersInRadius{r=25}
  - potion{type=WITHER;duration=100;level=1;force=true} @PlayersInRadius{r=35}
  - throw{velocity=5;velocityY=2} @PlayersInRadius{r=15}
  - throw{velocity=3;velocityY=1.5} @PlayersInRadius{r=25}

# REALITY TEAR - Creates dangerous void zones
NYXTHARA_REALITY_TEAR:
  Skills:
  - message{m="&5&l⚡ THE FABRIC OF REALITY TEARS APART! ⚡"} @PlayersInRadius{r=80}
  - sound{s=block.portal.ambient;v=1.0;p=0.3} @PlayersInRadius{r=80}
  - skill{s=NYXTHARA_CREATE_VOID_ZONE} @RandomLocationsNearTargets{a=5;r=20;minr=5}
  - delay 60
  - skill{s=NYXTHARA_CREATE_VOID_ZONE} @RandomLocationsNearTargets{a=3;r=25;minr=8}

NYXTHARA_CREATE_VOID_ZONE:
  Skills:
  - effect:particles{p=portal;a=60;hS=3;vS=5;s=1.0} @origin
  - sound{s=block.portal.trigger;v=0.6;p=0.5} @origin
  - delay 30
  - damage{a=6to10} @PlayersInRadius{r=5;origin=@origin}
  - potion{type=LEVITATION;duration=60;level=2;force=true} @PlayersInRadius{r=5;origin=@origin}
  - delay 30
  - damage{a=6to10} @PlayersInRadius{r=5;origin=@origin}
  - delay 30
  - damage{a=6to10} @PlayersInRadius{r=5;origin=@origin}
  - effect:particles{p=explosion_large;a=3;hS=0;vS=0;s=0} @origin

# VOID APOCALYPSE - Ultimate devastating attack
NYXTHARA_VOID_APOCALYPSE:
  Skills:
  - message{m="&5&l⚡⚡⚡ VOID APOCALYPSE - THE END OF ALL THINGS! ⚡⚡⚡"} @PlayersInRadius{r=100}
  - sound{s=entity.ender_dragon.death;v=1.0;p=0.2} @PlayersInRadius{r=100}
  - effect:particles{p=portal;a=500;hS=15;vS=15;s=3.0} @self
  - delay 60
  - damage{a=20to30} @PlayersInRadius{r=20}
  - damage{a=15to25} @PlayersInRadius{r=35}
  - damage{a=10to20} @PlayersInRadius{r=50}
  - potion{type=WITHER;duration=300;level=4;force=true} @PlayersInRadius{r=20}
  - potion{type=WITHER;duration=250;level=3;force=true} @PlayersInRadius{r=35}
  - potion{type=WITHER;duration=200;level=2;force=true} @PlayersInRadius{r=50}
  - throw{velocity=8;velocityY=3} @PlayersInRadius{r=20}
  - throw{velocity=6;velocityY=2} @PlayersInRadius{r=35}
  - throw{velocity=4;velocityY=1} @PlayersInRadius{r=50}

# Shadow projectile effects
NYXTHARA_SHADOW_PROJECTILE-Tick:
  Skills:
  - effect:particles{p=smoke_large;a=2;hS=0.3;vS=0.3;s=0.1} @origin

NYXTHARA_SHADOW_PROJECTILE-Hit:
  Skills:
  - damage{a=7to11} @target
  - potion{type=BLINDNESS;duration=80;level=1;force=true} @target
  - effect:particles{p=smoke_large;a=15;hS=1;vS=1;s=0.4} @target
  - sound{s=entity.generic.explode;v=0.4;p=1.4} @target

NYXTHARA_SHADOW_PROJECTILE-End:
  Skills:
  - effect:particles{p=smoke_large;a=10;hS=1;vS=1;s=0.3} @origin

# Rage messages
NYXTHARA_RAGE_RANDOMMESSAGE:
  Conditions:
  - stance{stance=NYXTHARA_VOID_RAGE} true
  Skills:
  - randomskill{
      skills=
      NYXTHARA_MESSAGE_1,
      NYXTHARA_MESSAGE_2,
      NYXTHARA_MESSAGE_3,
      NYXTHARA_MESSAGE_4,
      NYXTHARA_MESSAGE_5,
      NYXTHARA_MESSAGE_6,
      NYXTHARA_MESSAGE_7
      }

NYXTHARA_MESSAGE_1:
  Skills:
  - message{m="<mob.name>&f<&co>&r &5The void consumes all!"} @PlayersInRadius{r=60}
NYXTHARA_MESSAGE_2:
  Skills:
  - message{m="<mob.name>&f<&co>&r &5Your souls will feed the darkness!"} @PlayersInRadius{r=60}
NYXTHARA_MESSAGE_3:
  Skills:
  - message{m="<mob.name>&f<&co>&r &5Reality bends to my will!"} @PlayersInRadius{r=60}
NYXTHARA_MESSAGE_4:
  Skills:
  - message{m="<mob.name>&f<&co>&r &5Embrace the eternal void!"} @PlayersInRadius{r=60}
NYXTHARA_MESSAGE_5:
  Skills:
  - message{m="<mob.name>&f<&co>&r &5You cannot escape the shadows!"} @PlayersInRadius{r=60}
NYXTHARA_MESSAGE_6:
  Skills:
  - message{m="<mob.name>&f<&co>&r &5The darkness within me grows!"} @PlayersInRadius{r=60}
NYXTHARA_MESSAGE_7:
  Skills:
  - message{m="<mob.name>&f<&co>&r &5I am the sovereign of the void!"} @PlayersInRadius{r=60}
#
#
#
#
#
#
#
#
#
#
# 𝑪𝑹𝑬𝑨𝑻𝑬𝑫 𝑩𝒀 𝑽𝑬𝑿𝒀
# 𝑩𝑨𝑺𝑬𝑫 𝑶𝑵 𝑻𝑯𝑨𝑹𝑲𝑶𝑵 𝑩𝒀 𝑨𝑳𝑻𝑬𝑹𝑵𝑨𝑻𝑰𝑽𝑬𝑺𝑶𝑨𝑷
