#
#████████╗██╗░░██╗░█████╗░██████╗░██╗░░██╗░█████╗░███╗░░██╗  ██████╗░░█████╗░░██████╗░██████╗
#╚══██╔══╝██║░░██║██╔══██╗██╔══██╗██║░██╔╝██╔══██╗████╗░██║  ██╔══██╗██╔══██╗██╔════╝██╔════╝
#░░░██║░░░███████║███████║██████╔╝█████═╝░██║░░██║██╔██╗██║  ██████╦╝██║░░██║╚█████╗░╚█████╗░
#░░░██║░░░██╔══██║██╔══██║██╔══██╗██╔═██╗░██║░░██║██║╚████║  ██╔══██╗██║░░██║░╚═══██╗░╚═══██╗
#░░░██║░░░██║░░██║██║░░██║██║░░██║██║░╚██╗╚█████╔╝██║░╚███║  ██████╦╝╚█████╔╝██████╔╝██████╔╝
#░░░╚═╝░░░╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝░╚════╝░╚═╝░░╚══╝  ╚═════╝░░╚════╝░╚═════╝░╚═════╝░
#

SKILL_LEVITATOR:
  Cooldown: 60
  Skills:
  - setstance{stance=THARKON_LEVITATOR} @self
  - throw{velocity=8;velocityY=4} @EIR{r=4}
  - potion{t=LEVITATION;d=30;l=4;force=true;p=false} @Self
  - potion{t=DAMAGE_RESISTANCE;d=9999;l=10;force=true;p=false} @Self
  - potion{t=SLOW;d=9999;l=10;force=true;p=false} @Self
  - sound{s=entity.blaze.death;v=0.6;p=2} @self
  - sound{s=entity.blaze.shoot;v=1;p=1.2} @self
  - delay 45
  - skill{s=LEVITATOR-Effect}
  - delay 45
  - skill{s=LEVITATOR-Effect}
  - delay 45
  - skill{s=LEVITATOR-Effect}
  - delay 45
  - skill{s=LEVITATOR-Effect}
  - delay 45
  - skill{s=LEVITATOR-Effect}
  - delay 225
  - potion{t=DAMAGE_RESISTANCE;d=1;l=10;force=true;p=false} @Self
  - potion{t=SLOW;d=1;l=10;force=true;p=false} @Self
  - setstance{stance=THARKON_NORMAL} @self

LEVITATOR-Effect:
  Skills:
  - potion{t=LEVITATION;d=30;l=1;force=true;p=false} @Self
  - sound{s=entity.illusioner.cast_spell;v=1;p=0.7} @self
  - sound{s=entity.firework_rocket.large_blast;v=1;p=0.7} @PIR{r=30}
  - leap{velocity=250} @target
  - delay 30
  - sound{s=block.amethyst_block.break;v=1;p=1} @self
  - sound{s=entity.blaze.shoot;v=1.0;p=0.8} @self
  - projectile{onTick=LEVITATOR_PROJECTILE-Tick;onHit=LEVITATOR_PROJECTILE-Hit;onEnd=LEVITATOR_PROJECTILE-End;v=8;i=1;hR=1;vR=1;sB=false;g=-0.16;vO=-0.3;mr=13} @target
  - projectile{onTick=LEVITATOR_PROJECTILE-Tick;onHit=LEVITATOR_PROJECTILE-Hit;onEnd=LEVITATOR_PROJECTILE-End;v=8;i=1;hR=1;vR=1;sB=false;g=-0.16;HorizontalOffset=16;vO=-0.3;mr=13} @target
  - projectile{onTick=LEVITATOR_PROJECTILE-Tick;onHit=LEVITATOR_PROJECTILE-Hit;onEnd=LEVITATOR_PROJECTILE-End;v=8;i=1;hR=1;vR=1;sB=false;g=-0.16;HorizontalOffset=-16;vO=-0.3;mr=13} @target
  - projectile{onTick=LEVITATOR_PROJECTILE-Tick;onHit=LEVITATOR_PROJECTILE-Hit;onEnd=LEVITATOR_PROJECTILE-End;v=8;i=1;hR=1;vR=1;sB=false;g=-0.16;HorizontalOffset=32;vO=-0.3;mr=13} @target
  - projectile{onTick=LEVITATOR_PROJECTILE-Tick;onHit=LEVITATOR_PROJECTILE-Hit;onEnd=LEVITATOR_PROJECTILE-End;v=8;i=1;hR=1;vR=1;sB=false;g=-0.16;HorizontalOffset=-32;vO=-0.3;mr=13} @target

LEVITATOR_PROJECTILE-Tick:
  Skills:
  - particles{particle=FLAME;amount=1;hS=0;vS=0;speed=0} @origin
LEVITATOR_PROJECTILE-Hit:
  Skills:
  - damage{a=10to15}
  - ignite{ticks=100} 0.5
  - sound{s=enchant.thorns.hit;v=0.3;p=1}
LEVITATOR_PROJECTILE-End:
  Skills:
  - damage{a=4to8} @ENO{r=5}
  - sound{s=entity.lightning_bolt.impact;v=1;p=1} @ENO{r=5}
  - throw{velocity=8;velocityY=4} @ENO{r=5}
  - ignite{ticks=100} 0.5
  - fakeexplosion @origin
#
#
#
#
#
#
#
#
#
#
# 𝑴𝑨𝑫𝑬 𝑩𝒀 𝑨𝑳𝑻𝑬𝑹𝑵𝑨𝑻𝑰𝑽𝑬𝑺𝑶𝑨𝑷
# 𝑫𝑰𝑺𝑪𝑶𝑹𝑫: https://discord.gg/eUFRvyzJua