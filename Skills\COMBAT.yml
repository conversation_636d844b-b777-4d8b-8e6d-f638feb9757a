#
#████████╗██╗░░██╗░█████╗░██████╗░██╗░░██╗░█████╗░███╗░░██╗  ██████╗░░█████╗░░██████╗░██████╗
#╚══██╔══╝██║░░██║██╔══██╗██╔══██╗██║░██╔╝██╔══██╗████╗░██║  ██╔══██╗██╔══██╗██╔════╝██╔════╝
#░░░██║░░░███████║███████║██████╔╝█████═╝░██║░░██║██╔██╗██║  ██████╦╝██║░░██║╚█████╗░╚█████╗░
#░░░██║░░░██╔══██║██╔══██║██╔══██╗██╔═██╗░██║░░██║██║╚████║  ██╔══██╗██║░░██║░╚═══██╗░╚═══██╗
#░░░██║░░░██║░░██║██║░░██║██║░░██║██║░╚██╗╚█████╔╝██║░╚███║  ██████╦╝╚█████╔╝██████╔╝██████╔╝
#░░░╚═╝░░░╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝░╚════╝░╚═╝░░╚══╝  ╚═════╝░░╚════╝░╚═════╝░╚═════╝░
#
THARKON_COMBAT:
  Conditions:
  - playerwithin{d=2} true
  Skills:
  - damage{a=5to8;repeat=2;repeatinterval=2} @target
  - throw{velocity=3;velocityY=1} @target
  - ignite{ticks=100} @target 0.4

THARKON_COMBAT_FLYING:
  Cooldown: 10
  Conditions:
  - stance{stance=THARKON_NORMAL} true
  - altitude{a=>2}
  Skills:
  - setstance{stance=THARKON_COMBAT_FLYING} @self
  - potion{type=SLOW;duration=9999;level=10;force=true;p=false} @self
  - disengage @self
  - projectile{onTick=THARKON_PROJECTILE-Tick;onHit=THARKON_PROJECTILE-Hit;onEnd=THARKON_PROJECTILE-End;v=12;i=1;hR=1;vR=1;tyo=0.6;sfo=0;sso=2;eso=0} @target
  - delay 10
  - projectile{onTick=THARKON_PROJECTILE-Tick;onHit=THARKON_PROJECTILE-Hit;onEnd=THARKON_PROJECTILE-End;v=12;i=1;hR=1;vR=1;tyo=0.6;sfo=0;sso=-2;eso=0} @target
  - delay 10
  - projectile{onTick=THARKON_PROJECTILE-Tick;onHit=THARKON_PROJECTILE-Hit;onEnd=THARKON_PROJECTILE-End;v=12;i=1;hR=1;vR=1;tyo=1.6;sfo=0;sso=2;eso=0;g=0.12;hn=10} @target
  - delay 20
  - projectile{onTick=THARKON_PROJECTILE-Tick;onHit=THARKON_PROJECTILE-Hit;onEnd=THARKON_PROJECTILE-End;v=12;i=1;hR=1;vR=1;tyo=1.6;sfo=0;sso=-2;eso=0;g=0.12;hn=10} @target
  - delay 20
  - projectile{onTick=THARKON_PROJECTILE-Tick;onHit=THARKON_PROJECTILE-Hit;onEnd=THARKON_PROJECTILE-End;v=12;i=1;hR=1;vR=1;tyo=1.6;sfo=0;sso=2;eso=0;g=0.12;hn=10} @target
  - delay 20
  - projectile{onTick=THARKON_PROJECTILE-Tick;onHit=THARKON_PROJECTILE-Hit;onEnd=THARKON_PROJECTILE-End;v=12;i=1;hR=1;vR=1;tyo=1.6;sfo=0;sso=-2;eso=0;g=0.12;hn=10} @target
  - delay 20
  - projectile{onTick=THARKON_PROJECTILE-Tick;onHit=THARKON_PROJECTILE-Hit;onEnd=THARKON_PROJECTILE-End;v=12;i=1;hR=1;vR=1;tyo=1.6;sfo=0;sso=2;eso=0;g=0.12;hn=10} @target
  - delay 20
  - projectile{onTick=THARKON_PROJECTILE-Tick;onHit=THARKON_PROJECTILE-Hit;onEnd=THARKON_PROJECTILE-End;v=12;i=1;hR=1;vR=1;tyo=1.6;sfo=0;sso=-2;eso=0;g=0.12;hn=10} @target
  - delay 20
  - projectile{onTick=THARKON_PROJECTILE-Tick;onHit=THARKON_PROJECTILE-Hit;onEnd=THARKON_PROJECTILE-End;v=12;i=1;hR=1;vR=1;tyo=1.6;sfo=0;sso=2;eso=0;g=0.12;hn=10} @target
  - delay 10
  - projectile{onTick=THARKON_PROJECTILE-Tick;onHit=THARKON_PROJECTILE-Hit;onEnd=THARKON_PROJECTILE-End;v=12;i=1;hR=1;vR=1;tyo=1.6;sfo=0;sso=-2;eso=0;g=0.12;hn=10} @target
  - delay 10
  - projectile{onTick=THARKON_PROJECTILE-Tick;onHit=THARKON_PROJECTILE-Hit;onEnd=THARKON_PROJECTILE-End;v=12;i=1;hR=1;vR=1;tyo=0.6;sfo=0;sso=2;eso=0} @target
  - delay 10
  - projectile{onTick=THARKON_PROJECTILE-Tick;onHit=THARKON_PROJECTILE-Hit;onEnd=THARKON_PROJECTILE-End;v=12;i=1;hR=1;vR=1;tyo=0.6;sfo=0;sso=-2;eso=0} @target
  - potion{type=SLOW;duration=1;level=1;force=true;p=false} @self
  - setstance{stance=THARKON_NORMAL} @self

THARKON_PROJECTILE-Tick:
  Skills:
  - particles{particle=FLAME;amount=1;hS=0.1;vS=0.1;speed=0} @origin
  - particles{particle=LAVA;amount=1;hS=0.1;vS=0.1;speed=0} @origin

THARKON_PROJECTILE-Hit:
  Skills:
  - damage{a=5to10}
  - ignite{ticks=100} 0.5

THARKON_COMBAT_LUNGE:
  Cooldown: 10
  Conditions:
  - playerwithin{d=4} false
  - stance{stance=THARKON_NORMAL} true
  - onground true
  Skills:
  - lunge{velocity=1;velocityY=0.5} @Self
  - delay 10
  - leap{velocity=150} @target

THARKON_COMBAT_LEAP:
  Cooldown: 10
  Conditions:
  - playerwithin{d=10} false
  - stance{stance=THARKON_NORMAL} true
  Skills:
  - leap{velocity=250} @target
#
#
#
#
#
#
#
#
#
#
# 𝑴𝑨𝑫𝑬 𝑩𝒀 𝑨𝑳𝑻𝑬𝑹𝑵𝑨𝑻𝑰𝑽𝑬𝑺𝑶𝑨𝑷
# 𝑫𝑰𝑺𝑪𝑶𝑹𝑫: https://discord.gg/eUFRvyzJua