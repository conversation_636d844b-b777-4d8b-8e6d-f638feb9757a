#
#████████╗██╗░░██╗░█████╗░██████╗░██╗░░██╗░█████╗░███╗░░██╗  ██████╗░░█████╗░░██████╗░██████╗
#╚══██╔══╝██║░░██║██╔══██╗██╔══██╗██║░██╔╝██╔══██╗████╗░██║  ██╔══██╗██╔══██╗██╔════╝██╔════╝
#░░░██║░░░███████║███████║██████╔╝█████═╝░██║░░██║██╔██╗██║  ██████╦╝██║░░██║╚█████╗░╚█████╗░
#░░░██║░░░██╔══██║██╔══██║██╔══██╗██╔═██╗░██║░░██║██║╚████║  ██╔══██╗██║░░██║░╚═══██╗░╚═══██╗
#░░░██║░░░██║░░██║██║░░██║██║░░██║██║░╚██╗╚█████╔╝██║░╚███║  ██████╦╝╚█████╔╝██████╔╝██████╔╝
#░░░╚═╝░░░╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝░╚════╝░╚═╝░░╚══╝  ╚═════╝░░╚════╝░╚═════╝░╚═════╝░
#
SKILL_STORM:
  Cooldown: 60
  Skills:
  - setstance{stance=THARKON_STORM} @self
  - projectile{onTick=THARKON_PROJECTILE_STORM-Tick;onHit=THARKON_PROJECTILE_STORM-Hit;onEnd=THARKON_PROJECTILE_STORM-End;v=12;i=1;hR=1;vR=1;sfo=0;tyo=0;syo=30} @PIR{r=50} 0.5
  - projectile{onTick=THARKON_PROJECTILE_STORM-Tick;onHit=THARKON_PROJECTILE_STORM-Hit;onEnd=THARKON_PROJECTILE_STORM-End;v=12;i=1;hR=1;vR=1;sfo=-10to10;tyo=0;syo=30} @RandomLocationsNearOrigin{a=1to5;minradius=5;maxradius=25;s=5}
  - delay 10
  - projectile{onTick=THARKON_PROJECTILE_STORM-Tick;onHit=THARKON_PROJECTILE_STORM-Hit;onEnd=THARKON_PROJECTILE_STORM-End;v=12;i=1;hR=1;vR=1;sfo=0;tyo=0;syo=30} @PIR{r=50} 0.5
  - projectile{onTick=THARKON_PROJECTILE_STORM-Tick;onHit=THARKON_PROJECTILE_STORM-Hit;onEnd=THARKON_PROJECTILE_STORM-End;v=12;i=1;hR=1;vR=1;sfo=-10to10;tyo=0;syo=30} @RandomLocationsNearOrigin{a=1to5;minradius=5;maxradius=25;s=5}
  - delay 10
  - projectile{onTick=THARKON_PROJECTILE_STORM-Tick;onHit=THARKON_PROJECTILE_STORM-Hit;onEnd=THARKON_PROJECTILE_STORM-End;v=12;i=1;hR=1;vR=1;sfo=0;tyo=0;syo=30} @PIR{r=50} 0.5
  - projectile{onTick=THARKON_PROJECTILE_STORM-Tick;onHit=THARKON_PROJECTILE_STORM-Hit;onEnd=THARKON_PROJECTILE_STORM-End;v=12;i=1;hR=1;vR=1;sfo=-10to10;tyo=0;syo=30} @RandomLocationsNearOrigin{a=2to5;minradius=5;maxradius=25;s=5}
  - delay 10
  - projectile{onTick=THARKON_PROJECTILE_STORM-Tick;onHit=THARKON_PROJECTILE_STORM-Hit;onEnd=THARKON_PROJECTILE_STORM-End;v=12;i=1;hR=1;vR=1;sfo=0;tyo=0;syo=30} @PIR{r=50} 0.5
  - projectile{onTick=THARKON_PROJECTILE_STORM-Tick;onHit=THARKON_PROJECTILE_STORM-Hit;onEnd=THARKON_PROJECTILE_STORM-End;v=12;i=1;hR=1;vR=1;sfo=-10to10;tyo=0;syo=30} @RandomLocationsNearOrigin{a=2to5;minradius=5;maxradius=25;s=5}
  - delay 10
  - projectile{onTick=THARKON_PROJECTILE_STORM-Tick;onHit=THARKON_PROJECTILE_STORM-Hit;onEnd=THARKON_PROJECTILE_STORM-End;v=12;i=1;hR=1;vR=1;sfo=0;tyo=0;syo=30} @PIR{r=50} 0.5
  - projectile{onTick=THARKON_PROJECTILE_STORM-Tick;onHit=THARKON_PROJECTILE_STORM-Hit;onEnd=THARKON_PROJECTILE_STORM-End;v=12;i=1;hR=1;vR=1;sfo=-10to10;tyo=0;syo=30} @RandomLocationsNearOrigin{a=2to5;minradius=5;maxradius=25;s=5}
  - delay 10
  - projectile{onTick=THARKON_PROJECTILE_STORM-Tick;onHit=THARKON_PROJECTILE_STORM-Hit;onEnd=THARKON_PROJECTILE_STORM-End;v=12;i=1;hR=1;vR=1;sfo=0;tyo=0;syo=30} @PIR{r=50} 0.5
  - projectile{onTick=THARKON_PROJECTILE_STORM-Tick;onHit=THARKON_PROJECTILE_STORM-Hit;onEnd=THARKON_PROJECTILE_STORM-End;v=12;i=1;hR=1;vR=1;sfo=-10to10;tyo=0;syo=30} @RandomLocationsNearOrigin{a=2to5;minradius=5;maxradius=25;s=5}
  - delay 10
  - projectile{onTick=THARKON_PROJECTILE_STORM-Tick;onHit=THARKON_PROJECTILE_STORM-Hit;onEnd=THARKON_PROJECTILE_STORM-End;v=10;i=1;hR=1;vR=1;sfo=0;tyo=0;syo=30} @PIR{r=50} 0.5
  - projectile{onTick=THARKON_PROJECTILE_STORM-Tick;onHit=THARKON_PROJECTILE_STORM-Hit;onEnd=THARKON_PROJECTILE_STORM-End;v=10;i=1;hR=1;vR=1;sfo=-10to10;tyo=0;syo=30} @RandomLocationsNearOrigin{a=4to6;minradius=5;maxradius=25;s=5}
  - delay 10
  - projectile{onTick=THARKON_PROJECTILE_STORM-Tick;onHit=THARKON_PROJECTILE_STORM-Hit;onEnd=THARKON_PROJECTILE_STORM-End;v=8;i=1;hR=1;vR=1;sfo=0;tyo=0;syo=30} @PIR{r=50} 0.5
  - projectile{onTick=THARKON_PROJECTILE_STORM-Tick;onHit=THARKON_PROJECTILE_STORM-Hit;onEnd=THARKON_PROJECTILE_STORM-End;v=8;i=1;hR=1;vR=1;sfo=-10to10;tyo=0;syo=30} @RandomLocationsNearOrigin{a=4to6;minradius=5;maxradius=25;s=5}
  - delay 10
  - projectile{onTick=THARKON_PROJECTILE_STORM-Tick;onHit=THARKON_PROJECTILE_STORM-Hit;onEnd=THARKON_PROJECTILE_STORM-End;v=8;i=1;hR=1;vR=1;sfo=0;tyo=0;syo=30} @PIR{r=50} 0.5
  - projectile{onTick=THARKON_PROJECTILE_STORM-Tick;onHit=THARKON_PROJECTILE_STORM-Hit;onEnd=THARKON_PROJECTILE_STORM-End;v=8;i=1;hR=1;vR=1;sfo=-10to10;tyo=0;syo=30} @RandomLocationsNearOrigin{a=10to15;minradius=5;maxradius=25;s=5}
  - delay 10
  - projectile{onTick=THARKON_PROJECTILE_STORM-Tick;onHit=THARKON_PROJECTILE_STORM-Hit;onEnd=THARKON_PROJECTILE_STORM-End;v=8;i=1;hR=1;vR=1;sfo=0;tyo=0;syo=30} @PIR{r=50} 0.5
  - projectile{onTick=THARKON_PROJECTILE_STORM-Tick;onHit=THARKON_PROJECTILE_STORM-Hit;onEnd=THARKON_PROJECTILE_STORM-End;v=8;i=1;hR=1;vR=1;sfo=-10to10;tyo=0;syo=30} @RandomLocationsNearOrigin{a=10to15;minradius=5;maxradius=25;s=5}
  - delay 10
  - setstance{stance=THARKON_NORMAL} @self

THARKON_PROJECTILE_STORM-Tick:
  Skills:
  - particles{particle=SMALL_FLAME;amount=2;hS=0.1;vS=0.1;speed=0} @origin
  - particles{particle=SMOKE_NORMAL;amount=1;hS=0;vS=0;speed=0} @origin

THARKON_PROJECTILE_STORM-Hit:
  Skills:
  - damage{a=5to10}
  - ignite{ticks=100} 0.5

THARKON_PROJECTILE_STORM-End:
  Skills:
  - sound{s=block.lava.extinguish;v=0.3;p=1} @origin
  - blockmask{m=LAVA;r=1;d=60} @origin
  - damage{a=10} @ENO{r=2}
  - delay 10
  - damage{a=10} @ENO{r=2}
  - delay 10
  - damage{a=10} @ENO{r=2}
  - delay 10
  - damage{a=10} @ENO{r=2}
  - delay 10
  - damage{a=10} @ENO{r=2}
  - delay 10
  - damage{a=10} @ENO{r=2}
#
#
#
#
#
#
#
#
#
#
# 𝑴𝑨𝑫𝑬 𝑩𝒀 𝑨𝑳𝑻𝑬𝑹𝑵𝑨𝑻𝑰𝑽𝑬𝑺𝑶𝑨𝑷
# 𝑫𝑰𝑺𝑪𝑶𝑹𝑫: https://discord.gg/eUFRvyzJua