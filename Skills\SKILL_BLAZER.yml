#
#████████╗██╗░░██╗░█████╗░██████╗░██╗░░██╗░█████╗░███╗░░██╗  ██████╗░░█████╗░░██████╗░██████╗
#╚══██╔══╝██║░░██║██╔══██╗██╔══██╗██║░██╔╝██╔══██╗████╗░██║  ██╔══██╗██╔══██╗██╔════╝██╔════╝
#░░░██║░░░███████║███████║██████╔╝█████═╝░██║░░██║██╔██╗██║  ██████╦╝██║░░██║╚█████╗░╚█████╗░
#░░░██║░░░██╔══██║██╔══██║██╔══██╗██╔═██╗░██║░░██║██║╚████║  ██╔══██╗██║░░██║░╚═══██╗░╚═══██╗
#░░░██║░░░██║░░██║██║░░██║██║░░██║██║░╚██╗╚█████╔╝██║░╚███║  ██████╦╝╚█████╔╝██████╔╝██████╔╝
#░░░╚═╝░░░╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝░╚════╝░╚═╝░░╚══╝  ╚═════╝░░╚════╝░╚═════╝░╚═════╝░
#

SKILL_BLAZER:
  Cooldown: 60
  Skills:
  - setstance{stance=THARKON_BLAZER} @self
  - summon{type=THARKON_DUMMY;amount=1;radius=0;s=true} @randomLocationsNearCaster{a=5to10;minradius=3;maxradius=30;s=3}
  - delay 20
  - summon{type=THARKON_DUMMY;amount=1;radius=0;s=true} @randomLocationsNearCaster{a=5to10;minradius=3;maxradius=30;s=3}
  - delay 20
  - summon{type=THARKON_DUMMY;amount=1;radius=0;s=true} @randomLocationsNearCaster{a=5to10;minradius=3;maxradius=30;s=3}
  - delay 20
  - summon{type=THARKON_DUMMY;amount=1;radius=0;s=true} @randomLocationsNearCaster{a=5to10;minradius=3;maxradius=30;s=3}
  - setstance{stance=THARKON_NORMAL} @self

DUMMY_GEYSER:
  Skills:
  - geyser{type=LAVA;height=5;speed=10} @self
  - delay 1
  - damage{a=5} @PIR{r=1}
  - delay 10
  - damage{a=5} @PIR{r=1}
  - delay 10
  - damage{a=5} @PIR{r=1}
  - delay 10
  - damage{a=5} @PIR{r=1}
  - delay 10
  - skill{s=DUMMY_SHOOT}

DUMMY_SHOOT:
  Skills:
  - projectile{onTick=THARKON_PROJECTILE_BLAZER-Tick;onHit=THARKON_PROJECTILE-Hit;onEnd=THARKON_PROJECTILE-End;v=8;i=1;hR=1;vR=1;sfo=0;syo=5;tyo=0;mr=10;hO=0} @SelfLocation{y=0.0;foffset=5} 0.5
  - projectile{onTick=THARKON_PROJECTILE_BLAZER-Tick;onHit=THARKON_PROJECTILE-Hit;onEnd=THARKON_PROJECTILE-End;v=8;i=1;hR=1;vR=1;sfo=0;syo=5;tyo=0;mr=10;hO=90} @SelfLocation{y=0.0;foffset=5} 0.5
  - projectile{onTick=THARKON_PROJECTILE_BLAZER-Tick;onHit=THARKON_PROJECTILE-Hit;onEnd=THARKON_PROJECTILE-End;v=8;i=1;hR=1;vR=1;sfo=0;syo=5;tyo=0;mr=10;hO=180} @SelfLocation{y=0.0;foffset=5} 0.5
  - projectile{onTick=THARKON_PROJECTILE_BLAZER-Tick;onHit=THARKON_PROJECTILE-Hit;onEnd=THARKON_PROJECTILE-End;v=8;i=1;hR=1;vR=1;sfo=0;syo=5;tyo=0;mr=10;hO=270} @SelfLocation{y=0.0;foffset=5} 0.5

  - projectile{onTick=THARKON_PROJECTILE_BLAZER-Tick;onHit=THARKON_PROJECTILE-Hit;onEnd=THARKON_PROJECTILE-End;v=8;i=1;hR=1;vR=1;sfo=0;syo=5;tyo=0;mr=10;hO=45} @SelfLocation{y=0.0;foffset=5} 0.5
  - projectile{onTick=THARKON_PROJECTILE_BLAZER-Tick;onHit=THARKON_PROJECTILE-Hit;onEnd=THARKON_PROJECTILE-End;v=8;i=1;hR=1;vR=1;sfo=0;syo=5;tyo=0;mr=10;hO=135} @SelfLocation{y=0.0;foffset=5} 0.5
  - projectile{onTick=THARKON_PROJECTILE_BLAZER-Tick;onHit=THARKON_PROJECTILE-Hit;onEnd=THARKON_PROJECTILE-End;v=8;i=1;hR=1;vR=1;sfo=0;syo=5;tyo=0;mr=10;hO=225} @SelfLocation{y=0.0;foffset=5} 0.5
  - projectile{onTick=THARKON_PROJECTILE_BLAZER-Tick;onHit=THARKON_PROJECTILE-Hit;onEnd=THARKON_PROJECTILE-End;v=8;i=1;hR=1;vR=1;sfo=0;syo=5;tyo=0;mr=10;hO=315} @SelfLocation{y=0.0;foffset=5} 0.5
  - delay 60
  - remove @self

THARKON_PROJECTILE_BLAZER-Tick:
  Skills:
  - particles{particle=DRIPPING_DRIPSTONE_LAVA;amount=1;hS=0;vS=0;speed=0} @origin
  - particles{particle=LAVA;amount=1;hS=0;vS=0;speed=0} @origin
#
#
#
#
#
#
#
#
#
#
# 𝑴𝑨𝑫𝑬 𝑩𝒀 𝑨𝑳𝑻𝑬𝑹𝑵𝑨𝑻𝑰𝑽𝑬𝑺𝑶𝑨𝑷
# 𝑫𝑰𝑺𝑪𝑶𝑹𝑫: https://discord.gg/eUFRvyzJua