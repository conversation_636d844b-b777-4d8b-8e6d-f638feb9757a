#
#████████╗██╗░░██╗░█████╗░██████╗░██╗░░██╗░█████╗░███╗░░██╗  ██████╗░░█████╗░░██████╗░██████╗
#╚══██╔══╝██║░░██║██╔══██╗██╔══██╗██║░██╔╝██╔══██╗████╗░██║  ██╔══██╗██╔══██╗██╔════╝██╔════╝
#░░░██║░░░███████║███████║██████╔╝█████═╝░██║░░██║██╔██╗██║  ██████╦╝██║░░██║╚█████╗░╚█████╗░
#░░░██║░░░██╔══██║██╔══██║██╔══██╗██╔═██╗░██║░░██║██║╚████║  ██╔══██╗██║░░██║░╚═══██╗░╚═══██╗
#░░░██║░░░██║░░██║██║░░██║██║░░██║██║░╚██╗╚█████╔╝██║░╚███║  ██████╦╝╚█████╔╝██████╔╝██████╔╝
#░░░╚═╝░░░╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝░╚════╝░╚═╝░░╚══╝  ╚═════╝░░╚════╝░╚═════╝░╚═════╝░
#
THARKON_PLAYER_COUNT:
  Skills:
  - setmaxhealth{amount=2000;mode=STATIC} @self ?playersinradius{a=0-1;r=60}
  - healpercent{m=1.0} @self ?playersinradius{a=0-1;r=60}

  - setmaxhealth{amount=2250;mode=STATIC} @self ?playersinradius{a=2;r=60}
  - healpercent{m=1.0} @self ?playersinradius{a=2;r=60}

  - setmaxhealth{amount=2500;mode=STATIC} @self ?playersinradius{a=3;r=60}
  - healpercent{m=1.0} @self ?playersinradius{a=3;r=60}

  - setmaxhealth{amount=3000;mode=STATIC} @self ?playersinradius{a=4-99;r=60}
  - healpercent{m=1.0} @self ?playersinradius{a=4-99;r=60}

THARKON_PLAYER_COUNT_DMG:
  Skills:
  - setvariable{var=caster.tharkon_dmg;value="5";type=INTEGER} @self ?playersinradius{a=0-1;r=60}

  - setvariable{var=caster.tharkon_dmg;value="10";type=INTEGER} @self ?playersinradius{a=2;r=60}

  - setvariable{var=caster.tharkon_dmg;value="20";type=INTEGER} @self ?playersinradius{a=3;r=60}

  - setvariable{var=caster.tharkon_dmg;value="30";type=INTEGER} @self ?playersinradius{a=4-99;r=60}
#
#
#
#
#
#
#
#
#
#
# 𝑴𝑨𝑫𝑬 𝑩𝒀 𝑨𝑳𝑻𝑬𝑹𝑵𝑨𝑻𝑰𝑽𝑬𝑺𝑶𝑨𝑷
# 𝑫𝑰𝑺𝑪𝑶𝑹𝑫: https://discord.gg/eUFRvyzJua