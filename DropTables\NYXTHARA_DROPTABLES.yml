#
#███╗░░██╗██╗░░░██╗██╗░░██╗██╗████████╗██╗░░██╗░█████╗░██████╗░░█████╗░
#████╗░██║╚██╗░██╔╝╚██╗██╔╝╚═╝╚══██╔══╝██║░░██║██╔══██╗██╔══██╗██╔══██╗
#██╔██╗██║░╚████╔╝░░╚███╔╝░░░░░░░██║░░░███████║███████║██████╔╝███████║
#██║╚████║░░╚██╔╝░░░██╔██╗░░░░░░░██║░░░██╔══██║██╔══██║██╔══██╗██╔══██║
#██║░╚███║░░░██║░░░██╔╝╚██╗░░░░░░██║░░░██║░░██║██║░░██║██║░░██║██║░░██║
#╚═╝░░╚══╝░░░╚═╝░░░╚═╝░░╚═╝░░░░░░╚═╝░░░╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝
#
# VOID SOVEREIGN DROP TABLES - Created by vexy
#

NYXTHARA_DROPS:
  TotalItems: 3
  MinItems: 2
  MaxItems: 2
  BonusLuckItems: 0.15
  Drops:
  # Void-themed rare items
  - wither_skeleton_skull 1 0.18
  - nether_star 1 0.12
  - end_crystal 1 0.10
  - dragon_head 1 0.08
  - beacon 1 0.06
  - conduit 1 0.09
  - heart_of_the_sea 1 0.07
  - totem_of_undying 1 0.11
  - elytra 1 0.13
  - shulker_shell 1 0.10
  # Enhanced materials
  - netherite_ingot 1 0.05
  - ancient_debris 1 0.04
  - crying_obsidian 1 0.15
  - obsidian 1 0.20
  - end_stone 1 0.18
  - purpur_block 1 0.16
  - blackstone 1 0.22
  # Valuable items
  - diamond 1 0.25
  - emerald 1 0.20
  - enchanted_golden_apple 1 0.08
  - golden_apple 1 0.15
  - experience_bottle 1 0.30
  # Void-specific drops
  - ender_pearl 1 0.25
  - ender_eye 1 0.12
  - ghast_tear 1 0.14
  - blaze_powder 1 0.18
  - magma_cream 1 0.16

NYXTHARA_VOID_ARTIFACTS:
  TotalItems: 2
  MinItems: 1
  MaxItems: 1
  BonusLuckItems: 0.12
  Drops:
  # Custom void artifacts (these would be custom items)
  - VOID_SOVEREIGN_CROWN 1 0.03
  - SHADOW_BLADE 1 0.05
  - VOID_CRYSTAL 1 0.08
  - DIMENSIONAL_SHARD 1 0.10
  - NYXTHARA_ESSENCE 1 0.12
  - VOID_CLOAK 1 0.04
  - SHADOW_BOOTS 1 0.06
  - VOID_STAFF 1 0.04
  - DIMENSIONAL_KEY 1 0.07
  - VOID_ORB 1 0.09
  # Enhanced equipment
  - netherite_sword 1 0.08
  - netherite_helmet 1 0.06
  - netherite_chestplate 1 0.05
  - netherite_leggings 1 0.06
  - netherite_boots 1 0.07
  - netherite_axe 1 0.07
  - netherite_pickaxe 1 0.06
  - bow 1 0.12
  - crossbow 1 0.10
  - trident 1 0.09

NYXTHARA_RARE_MATERIALS:
  TotalItems: 4
  MinItems: 2
  MaxItems: 3
  BonusLuckItems: 0.08
  Drops:
  # Rare crafting materials
  - netherite_scrap 1 0.15
  - diamond 1 0.30
  - emerald 1 0.25
  - gold_ingot 1 0.35
  - iron_ingot 1 0.40
  - redstone 1 0.45
  - lapis_lazuli 1 0.38
  - quartz 1 0.42
  - glowstone_dust 1 0.35
  - gunpowder 1 0.40
  - string 1 0.45
  - bone 1 0.50
  - rotten_flesh 1 0.55
  - spider_eye 1 0.48
  - blaze_rod 1 0.20
  - ghast_tear 1 0.18
  - magma_cream 1 0.22
  - slime_ball 1 0.28
  - prismarine_shard 1 0.25
  - prismarine_crystals 1 0.20

NYXTHARA_ENCHANTED_BOOKS:
  TotalItems: 2
  MinItems: 1
  MaxItems: 1
  BonusLuckItems: 0.10
  Drops:
  # High-level enchanted books
  - enchanted_book{StoredEnchantments:[{id:"minecraft:sharpness",lvl:5}]} 1 0.08
  - enchanted_book{StoredEnchantments:[{id:"minecraft:protection",lvl:4}]} 1 0.10
  - enchanted_book{StoredEnchantments:[{id:"minecraft:unbreaking",lvl:3}]} 1 0.12
  - enchanted_book{StoredEnchantments:[{id:"minecraft:mending",lvl:1}]} 1 0.06
  - enchanted_book{StoredEnchantments:[{id:"minecraft:efficiency",lvl:5}]} 1 0.09
  - enchanted_book{StoredEnchantments:[{id:"minecraft:fortune",lvl:3}]} 1 0.07
  - enchanted_book{StoredEnchantments:[{id:"minecraft:silk_touch",lvl:1}]} 1 0.08
  - enchanted_book{StoredEnchantments:[{id:"minecraft:power",lvl:5}]} 1 0.09
  - enchanted_book{StoredEnchantments:[{id:"minecraft:infinity",lvl:1}]} 1 0.05
  - enchanted_book{StoredEnchantments:[{id:"minecraft:feather_falling",lvl:4}]} 1 0.11
  - enchanted_book{StoredEnchantments:[{id:"minecraft:respiration",lvl:3}]} 1 0.10
  - enchanted_book{StoredEnchantments:[{id:"minecraft:aqua_affinity",lvl:1}]} 1 0.12
  - enchanted_book{StoredEnchantments:[{id:"minecraft:depth_strider",lvl:3}]} 1 0.09
  - enchanted_book{StoredEnchantments:[{id:"minecraft:frost_walker",lvl:2}]} 1 0.07
  - enchanted_book{StoredEnchantments:[{id:"minecraft:soul_speed",lvl:3}]} 1 0.06
  - enchanted_book{StoredEnchantments:[{id:"minecraft:swift_sneak",lvl:3}]} 1 0.05
  - enchanted_book{StoredEnchantments:[{id:"minecraft:looting",lvl:3}]} 1 0.08
  - enchanted_book{StoredEnchantments:[{id:"minecraft:fire_aspect",lvl:2}]} 1 0.10
  - enchanted_book{StoredEnchantments:[{id:"minecraft:knockback",lvl:2}]} 1 0.11
  - enchanted_book{StoredEnchantments:[{id:"minecraft:sweeping",lvl:3}]} 1 0.09
#
#
#
#
#
#
#
#
#
#
# 𝑪𝑹𝑬𝑨𝑻𝑬𝑫 𝑩𝒀 𝑽𝑬𝑿𝒀
# 𝑩𝑨𝑺𝑬𝑫 𝑶𝑵 𝑻𝑯𝑨𝑹𝑲𝑶𝑵 𝑩𝒀 𝑨𝑳𝑻𝑬𝑹𝑵𝑨𝑻𝑰𝑽𝑬𝑺𝑶𝑨𝑷
