#
#███╗░░██╗██╗░░░██╗██╗░░██╗██╗████████╗██╗░░██╗░█████╗░██████╗░░█████╗░
#████╗░██║╚██╗░██╔╝╚██╗██╔╝╚═╝╚══██╔══╝██║░░██║██╔══██╗██╔══██╗██╔══██╗
#██╔██╗██║░╚████╔╝░░╚███╔╝░░░░░░░██║░░░███████║███████║██████╔╝███████║
#██║╚████║░░╚██╔╝░░░██╔██╗░░░░░░░██║░░░██╔══██║██╔══██║██╔══██╗██╔══██║
#██║░╚███║░░░██║░░░██╔╝╚██╗░░░░░░██║░░░██║░░██║██║░░██║██║░░██║██║░░██║
#╚═╝░░╚══╝░░░╚═╝░░░╚═╝░░╚═╝░░░░░░╚═╝░░░╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝
#
# THE VOID SOVEREIGN - Created by vexy
#
NYXTHARA:
  MobType: WITHER_SKELETON
  Display: '<gradient:#8a2be2:#4b0082><bold>Nyx''thara, the Void Sovereign</gradient>'
  Health: 100 # Controlled by NYXTHARA_PLAYER_COUNT skill
  Damage: 1
  BossBar:
    Enabled: true
    Title: '<gradient:#8a2be2:#4b0082><bold>Nyx''thara, the Void Sovereign</gradient>'
    Range: 100
    Color: PURPLE
    Style: SEGMENTED_20
    CreateFog: true
    DarkenSky: true
    PlayBossMusic: true
  Modules:
    ThreatTable: true
  Faction: VOID_SOVEREIGN
  AIGoalSelectors:
  - clear
  AITargetSelectors:
  - clear
  Options:
    AlwaysShowName: true
    FollowRange: 100
    MaxCombatDistance: 80
    Despawn: false
    PreventRenaming: true
    PreventSunburn: true
    KnockbackResistance: 1
    PreventOtherDrops: true
    Silent: true
    MovementSpeed: 0.4
  DamageModifiers:
  - ENTITY_ATTACK 0.7
  - PROJECTILE 1.2
  - MAGIC 0.5
  - VOID -10
  - WITHER -10
  - POISON -10
  - BLOCK_EXPLOSION -10
  - FIRE 1.5
  - FIRE_TICK 1.5
  - HOT_FLOOR 1.5
  - LAVA 1.5
  - SUFFOCATION 0
  KillMessages:
  - '&f<target.name> &5was consumed by the void through <caster.display>'
  - '&f<target.name> &5had their soul claimed by <caster.display>'
  - '&f<target.name> &5was erased from existence by <caster.display>'
  Drops:
  - NYXTHARA_DROPS 1 1
  - NYXTHARA_VOID_ARTIFACTS 1 1
  DropOptions:
    DropMethod: FANCY
    PerPlayerDrops: true
    RequiredDamagePercent: 70
    HologramItemNames: true
    ItemGlowByDefault: true
    ShowDeathHologram: true
    ClientSideDrops: true
    Lootsplosion: true
    HologramTimeout: 400
    HologramMessage:
    - '<#8a2be2>◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆'
    - '<mob.name> - <mob.hp>HP'
    - '<#4b0082>⟨ VOID SOVEREIGN DEFEATED ⟩'
    - ''
    - '<#8a2be2>1st Place | <1.name> | <1.damage>'
    - '<#8a2be2>2nd Place | <2.name> | <2.damage>'
    - '<#8a2be2>3rd Place | <3.name> | <3.damage>'
    - '<#8a2be2>4th Place | <4.name> | <4.damage>'
    - '<#8a2be2>5th Place | <5.name> | <5.damage>'
    - ''
    - 'Your rank: #<player.rank> | <player.damage>'
    - '<#8a2be2>◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆'
  Skills:
# Ambient Sounds - Void/Shadow themed
  - sound{s=entity.wither.ambient;v=0.3;p=0.8} @self ~onTimer:140 0.6
  - sound{s=entity.enderman.ambient;v=0.4;p=0.5} @self ~onTimer:160 0.7
  - sound{s=entity.wither.hurt;v=0.4;p=0.8} @self ~onDamaged 1
  - sound{s=entity.enderman.hurt;v=0.3;p=0.6} @self ~onDamaged 1
  - sound{s=entity.wither.death;v=0.6;p=0.5} @self ~onDeath 1
  - sound{s=entity.ender_dragon.death;v=0.4;p=0.3} @self ~onDeath 1
#
# Core Skills
  - setstance{stance=NYXTHARA_NORMAL} @self ~onSpawn 1
  - skill{s=NYXTHARA_PLAYER_COUNT} ~onSpawn 1
  - skill{s=NYXTHARA_ONSPAWN} ~onSpawn 1
  - skill{s=NYXTHARA_ONSPAWN_AI} ~onEnterCombat 1
#
# Combat Skills
  - skill{s=NYXTHARA_COMBAT} ~onTimer:5 1
  - skill{s=NYXTHARA_VOID_PHASE} ~onTimer:120 0.9
  - skill{s=NYXTHARA_SHADOW_STEP} ~onTimer:80 0.8
#
# Effects and Abilities
  - skill{s=NYXTHARA_VOID_AURA} ~onTimer:30 1
  - skill{s=NYXTHARA_EFFECT_DAMAGED} ~onDamaged 1
  - skill{s=NYXTHARA_DIMENSIONAL_RIFT} ~onTimer:150 0.7
#
# Special Abilities
  - skill{s=NYXTHARA_RANDOMSKILL_SELECTOR} ~onTimer:100 1
  - skill{s=NYXTHARA_VOID_RAGE} <20% ~onTimer:200 1
  - skill{s=NYXTHARA_SHADOW_CLONE} <50% ~onTimer:180 0.6
#
# Death
  - skill{s=NYXTHARA_VICTORY;forcesync=true} ~onDeath 1
#
# Experience Reward
  - giveexperiencelevels{a=75to125} @PIR{r=60} ~onDeath 1
#
#
#
#
#
#
#
#
#
#
# 𝑪𝑹𝑬𝑨𝑻𝑬𝑫 𝑩𝒀 𝑽𝑬𝑿𝒀
# 𝑩𝑨𝑺𝑬𝑫 𝑶𝑵 𝑻𝑯𝑨𝑹𝑲𝑶𝑵 𝑩𝒀 𝑨𝑳𝑻𝑬𝑹𝑵𝑨𝑻𝑰𝑽𝑬𝑺𝑶𝑨𝑷
