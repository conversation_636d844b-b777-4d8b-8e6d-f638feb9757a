#
#████████╗██╗░░██╗░█████╗░██████╗░██╗░░██╗░█████╗░███╗░░██╗  ██████╗░░█████╗░░██████╗░██████╗
#╚══██╔══╝██║░░██║██╔══██╗██╔══██╗██║░██╔╝██╔══██╗████╗░██║  ██╔══██╗██╔══██╗██╔════╝██╔════╝
#░░░██║░░░███████║███████║██████╔╝█████═╝░██║░░██║██╔██╗██║  ██████╦╝██║░░██║╚█████╗░╚█████╗░
#░░░██║░░░██╔══██║██╔══██║██╔══██╗██╔═██╗░██║░░██║██║╚████║  ██╔══██╗██║░░██║░╚═══██╗░╚═══██╗
#░░░██║░░░██║░░██║██║░░██║██║░░██║██║░╚██╗╚█████╔╝██║░╚███║  ██████╦╝╚█████╔╝██████╔╝██████╔╝
#░░░╚═╝░░░╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝░╚════╝░╚═╝░░╚══╝  ╚═════╝░░╚════╝░╚═════╝░╚═════╝░
#
THARKON:
  MobType: BLAZE
  Display: '<gradient:#ff0022:#e600ff><bold>Ragnis Tharkon</gradient>'
  Health: 100 # This is not being used, health are being controlled by a skill "THARKON_PLAYER_COUNT" located in "DEPENDING_ON_PLAYER_COUNT_SKILLS"
  Damage: 1
  BossBar:
    Enabled: true
    Title: '<gradient:#ff0022:#e600ff><bold>Ragnis Tharkon</gradient>'
    Range: 80
    Color: RED
    Style: SOLID
    CreateFog: true
    DarkenSky: true
  Modules:
    ThreatTable: true
  Faction: THARKON
  AIGoalSelectors:
  - clear
  AITargetSelectors:
  - clear
  Options:
    AlwaysShowName: true
    FollowRange: 80
    MaxCombatDistance: 60
    Despawn: false
    PreventRenaming: true
    PreventSunburn: true
    KnockbackResistance: 1
    PreventOtherDrops: true
    Silent: true
  DamageModifiers:
  - ENTITY_ATTACK 0.8
  - PROJECTILE 1.4
  - BLOCK_EXPLOSION -10
  - FIRE -10
  - FIRE_TICK -10
  - HOT_FLOOR -10
  - LAVA -10
  - SUFFOCATION 0
  KillMessages:
  - '&f<target.name> &7was slain by <caster.display>'
  Drops:
  - THARKON_DROPS 1 1
  - THARKON_TOOLS 1 1
  DropOptions:
    DropMethod: FANCY
    PerPlayerDrops: true
    RequiredDamagePercent: 75
    HologramItemNames: true
    ItemGlowByDefault: true
    ShowDeathHologram: true
    ClientSideDrops: true
    Lootsplosion: true
    HologramTimeout: 300
    HologramMessage:
    - '<#ff0022>========================'
    - '<mob.name> - <mob.hp>HP'
    - ''
    - '<#ff0022>1st Place | <1.name> | <1.damage>'
    - '<#ff0022>2nd Place | <2.name> | <2.damage>'
    - '<#ff0022>3rd Place | <3.name> | <3.damage>'
    - '<#ff0022>4th Place | <4.name> | <4.damage>'
    - '<#ff0022>5th Place | <5.name> | <5.damage>'
    - ''
    - 'Your rank: #<player.rank> | <player.damage>'
    - '<#ff0022>========================'
  Skills:
# Sounds
  - sound{s=entity.allay.ambient_without_item;v=0.5;p=0.2} @self ~onTimer:100 0.5
  - sound{s=entity.allay.ambient_with_item;v=0.5;p=0.2} @self ~onTimer:120 0.5
  - sound{s=entity.ender_dragon.hurt;v=0.5;p=1} @self ~onDamaged 1
  - sound{s=entity.endermite.hurt;v=0.5;p=1} @self ~onDamaged 1
  - sound{s=entity.ender_dragon.death;v=0.5;p=1.5} @self ~onDeath 1
#
#
# Skills
  - setstance{stance=THARKON_NORMAL} @self ~onSpawn 1
  - skill{s=THARKON_PLAYER_COUNT} ~onSpawn 1
  - skill{s=THARKON_ONSPAWN} ~onSpawn 1
  - skill{s=THARKON_ONSPAWN_AI} ~onEnterCombat 1
#
  - skill{s=THARKON_COMBAT} ~onTimer:5 1
  - skill{s=THARKON_COMBAT_FLYING} ~onTimer:100 0.8
#
  - skill{s=THARKON_EFFECT_GROUND} ~onTimer:40 1
  - skill{s=THARKON_EFFECT_DAMAGED} ~onDamaged 1
  - skill{s=THARKON_EFFECT_FLYDOWN} ~onTimer:60 1
  - skill{s=THARKON_COMBAT_LUNGE} ~onTimer:100 1
  - skill{s=THARKON_COMBAT_LEAP} ~onTimer:100 1
#
  - skill{s=THARKON_RANDOMSKILL_SELECTOR} ~onTimer:100 1
  - skill{s=THARKON_RAGE} <25% ~onTimer:300 1
#
  - skill{s=THARKON_WIN;forcesync=true} ~onDeath 1
#
#
#
  - giveexperiencelevels{a=50to100} @PIR{r=50} ~onDeath 1
#
#
#
#
#
#
#
#
#
#
# 𝑴𝑨𝑫𝑬 𝑩𝒀 𝑨𝑳𝑻𝑬𝑹𝑵𝑨𝑻𝑰𝑽𝑬𝑺𝑶𝑨𝑷
# 𝑫𝑰𝑺𝑪𝑶𝑹𝑫: https://discord.gg/eUFRvyzJua