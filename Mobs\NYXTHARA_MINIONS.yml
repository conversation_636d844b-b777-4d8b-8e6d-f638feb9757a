#
#███╗░░██╗██╗░░░██╗██╗░░██╗██╗████████╗██╗░░██╗░█████╗░██████╗░░█████╗░
#████╗░██║╚██╗░██╔╝╚██╗██╔╝╚═╝╚══██╔══╝██║░░██║██╔══██╗██╔══██╗██╔══██╗
#██╔██╗██║░╚████╔╝░░╚███╔╝░░░░░░░██║░░░███████║███████║██████╔╝███████║
#██║╚████║░░╚██╔╝░░░██╔██╗░░░░░░░██║░░░██╔══██║██╔══██║██╔══██╗██╔══██║
#██║░╚███║░░░██║░░░██╔╝╚██╗░░░░░░██║░░░██║░░██║██║░░██║██║░░██║██║░░██║
#╚═╝░░╚══╝░░░╚═╝░░░╚═╝░░╚═╝░░░░░░╚═╝░░░╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝
#
# VOID SOVEREIGN MINIONS - Created by vexy
#

# <PERSON> Clone - Temporary duplicate of the boss
NYXTHARA_SHADOW_CLONE:
  MobType: WITHER_SKELETON
  Display: '<gradient:#4b0082:#2e0054><bold>Shadow of Nyx''thara</gradient>'
  Health: 150
  Damage: 1
  Faction: VOID_SOVEREIGN
  AIGoalSelectors:
  - clear
  AITargetSelectors:
  - clear
  Options:
    AlwaysShowName: true
    FollowRange: 40
    MaxCombatDistance: 30
    Despawn: true
    DespawnOnNoTarget: true
    PreventRenaming: true
    PreventSunburn: true
    KnockbackResistance: 0.8
    PreventOtherDrops: true
    Silent: true
    MovementSpeed: 0.35
  DamageModifiers:
  - ENTITY_ATTACK 0.9
  - PROJECTILE 1.0
  - MAGIC 0.7
  - VOID -10
  - WITHER -10
  - POISON -10
  KillMessages:
  - '&f<target.name> &5was consumed by <caster.display>'
  Drops: []
  Skills:
  # Ambient effects
  - effect:particles{p=smoke_normal;a=5;hS=1;vS=1;s=0.2} @self ~onTimer:40 1
  - sound{s=entity.enderman.ambient;v=0.2;p=0.7} @self ~onTimer:180 0.5
  # Combat
  - skill{s=SHADOW_CLONE_COMBAT} ~onTimer:5 1
  - skill{s=SHADOW_CLONE_TELEPORT} ~onTimer:60 0.7
  # Despawn after time
  - remove @self ~onTimer:600 1

# Void Wraith - Summoned during special attacks
NYXTHARA_VOID_WRAITH:
  MobType: VEX
  Display: '<gradient:#2e0054:#1a0030><bold>Void Wraith</gradient>'
  Health: 80
  Damage: 1
  Faction: VOID_SOVEREIGN
  AIGoalSelectors:
  - clear
  AITargetSelectors:
  - clear
  Options:
    AlwaysShowName: true
    FollowRange: 30
    MaxCombatDistance: 25
    Despawn: true
    DespawnOnNoTarget: true
    PreventRenaming: true
    PreventSunburn: true
    KnockbackResistance: 0.5
    PreventOtherDrops: true
    Silent: true
    MovementSpeed: 0.4
  DamageModifiers:
  - ENTITY_ATTACK 1.2
  - PROJECTILE 0.8
  - MAGIC 0.5
  - VOID -10
  - WITHER -10
  KillMessages:
  - '&f<target.name> &5was drained by <caster.display>'
  Drops: []
  Skills:
  # Ambient effects
  - effect:particles{p=portal;a=3;hS=0.5;vS=0.5;s=0.1} @self ~onTimer:30 1
  - sound{s=entity.vex.ambient;v=0.3;p=0.5} @self ~onTimer:120 0.4
  # Combat
  - skill{s=VOID_WRAITH_COMBAT} ~onTimer:5 1
  - skill{s=VOID_WRAITH_DRAIN} ~onTimer:40 0.8
  # Despawn after time
  - remove @self ~onTimer:300 1

# Shadow Clone Combat Skills
SHADOW_CLONE_COMBAT:
  Conditions:
  - playerwithin{d=4} true
  Skills:
  - damage{a=4to7} @target
  - throw{velocity=2;velocityY=0.5} @target
  - potion{type=WITHER;duration=60;level=1;force=true} @target 0.2
  - effect:particles{p=smoke_large;a=10;hS=1;vS=1;s=0.3} @target

SHADOW_CLONE_TELEPORT:
  Cooldown: 8
  Conditions:
  - playerwithin{d=15} true
  - playerwithin{d=3} false
  Skills:
  - effect:particles{p=smoke_large;a=20;hS=1.5;vS=1.5;s=0.4} @self
  - sound{s=entity.enderman.teleport;v=0.4;p=0.9} @self
  - teleport{l=@target{limit=1};yoffset=0;xoffset=-2;zoffset=-2} @self
  - delay 2
  - effect:particles{p=portal;a=15;hS=1;vS=1;s=0.2} @self

# Void Wraith Combat Skills
VOID_WRAITH_COMBAT:
  Conditions:
  - playerwithin{d=3} true
  Skills:
  - damage{a=3to5} @target
  - potion{type=HUNGER;duration=80;level=1;force=true} @target 0.3
  - effect:particles{p=portal;a=8;hS=0.8;vS=0.8;s=0.2} @target

VOID_WRAITH_DRAIN:
  Conditions:
  - playerwithin{d=8} true
  Skills:
  - damage{a=2to4} @PlayersInRadius{r=6}
  - potion{type=SLOWNESS;duration=60;level=1;force=true} @PlayersInRadius{r=6} 0.4
  - effect:particles{p=portal;a=15;hS=2;vS=1;s=0.3} @self
  - sound{s=entity.vex.charge;v=0.3;p=0.8} @self
#
#
#
#
#
#
#
#
#
#
# 𝑪𝑹𝑬𝑨𝑻𝑬𝑫 𝑩𝒀 𝑽𝑬𝑿𝒀
# 𝑩𝑨𝑺𝑬𝑫 𝑶𝑵 𝑻𝑯𝑨𝑹𝑲𝑶𝑵 𝑩𝒀 𝑨𝑳𝑻𝑬𝑹𝑵𝑨𝑻𝑰𝑽𝑬𝑺𝑶𝑨𝑷
