#
#████████╗██╗░░██╗░█████╗░██████╗░██╗░░██╗░█████╗░███╗░░██╗  ██████╗░░█████╗░░██████╗░██████╗
#╚══██╔══╝██║░░██║██╔══██╗██╔══██╗██║░██╔╝██╔══██╗████╗░██║  ██╔══██╗██╔══██╗██╔════╝██╔════╝
#░░░██║░░░███████║███████║██████╔╝█████═╝░██║░░██║██╔██╗██║  ██████╦╝██║░░██║╚█████╗░╚█████╗░
#░░░██║░░░██╔══██║██╔══██║██╔══██╗██╔═██╗░██║░░██║██║╚████║  ██╔══██╗██║░░██║░╚═══██╗░╚═══██╗
#░░░██║░░░██║░░██║██║░░██║██║░░██║██║░╚██╗╚█████╔╝██║░╚███║  ██████╦╝╚█████╔╝██████╔╝██████╔╝
#░░░╚═╝░░░╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝░╚════╝░╚═╝░░╚══╝  ╚═════╝░░╚════╝░╚═════╝░╚═════╝░
#
SKILL_IGNIS:
  Cooldown: 60
  Skills:
  - setstance{stance=THARKON_IGNIS} @self
  - projectile{onTick=SKILL_IGNIS-Tick;onHit=SKILL_IGNIS-Hit;onEnd=SKILL_IGNIS-End;v=6;i=1;hR=1;vR=1;hp=false;hnp=false;sfo=8;sE=false;syo=10;mr=25} @RandomLocationsNearCaster{a=1;minr=5;maxradius=25;s=4}
  - projectile{onTick=SKILL_IGNIS-Tick;onHit=SKILL_IGNIS-Hit;onEnd=SKILL_IGNIS-End;v=6;i=1;hR=1;vR=1;hp=false;hnp=false;sfo=-8;sE=false;syo=10;mr=25} @RandomLocationsNearCaster{a=1;minr=5;maxradius=25;s=4}
  - projectile{onTick=SKILL_IGNIS-Tick;onHit=SKILL_IGNIS-Hit;onEnd=SKILL_IGNIS-End;v=6;i=1;hR=1;vR=1;hp=false;hnp=false;sfo=0;soffset=8;sE=false;syo=10;mr=25} @RandomLocationsNearCaster{a=1;minr=5;maxradius=25;s=4}
  - projectile{onTick=SKILL_IGNIS-Tick;onHit=SKILL_IGNIS-Hit;onEnd=SKILL_IGNIS-End;v=6;i=1;hR=1;vR=1;hp=false;hnp=false;sfo=0;soffset=-8;sE=false;syo=10;mr=25} @RandomLocationsNearCaster{a=1;minr=5;maxradius=25;s=4}
  - delay 20
  - projectile{onTick=SKILL_IGNIS-Tick;onHit=SKILL_IGNIS-Hit;onEnd=SKILL_IGNIS-End;v=6;i=1;hR=1;vR=1;hp=false;hnp=false;sfo=8;sE=false;syo=10;mr=25} @RandomLocationsNearCaster{a=1;minr=5;maxradius=25;s=4}
  - projectile{onTick=SKILL_IGNIS-Tick;onHit=SKILL_IGNIS-Hit;onEnd=SKILL_IGNIS-End;v=6;i=1;hR=1;vR=1;hp=false;hnp=false;sfo=-8;sE=false;syo=10;mr=25} @RandomLocationsNearCaster{a=1;minr=5;maxradius=25;s=4}
  - projectile{onTick=SKILL_IGNIS-Tick;onHit=SKILL_IGNIS-Hit;onEnd=SKILL_IGNIS-End;v=6;i=1;hR=1;vR=1;hp=false;hnp=false;sfo=0;soffset=8;sE=false;syo=10;mr=25} @RandomLocationsNearCaster{a=1;minr=5;maxradius=25;s=4}
  - projectile{onTick=SKILL_IGNIS-Tick;onHit=SKILL_IGNIS-Hit;onEnd=SKILL_IGNIS-End;v=6;i=1;hR=1;vR=1;hp=false;hnp=false;sfo=0;soffset=-8;sE=false;syo=10;mr=25} @RandomLocationsNearCaster{a=1;minr=5;maxradius=25;s=4}
  - delay 20
  - projectile{onTick=SKILL_IGNIS-Tick;onHit=SKILL_IGNIS-Hit;onEnd=SKILL_IGNIS-End;v=6;i=1;hR=1;vR=1;hp=false;hnp=false;sfo=8;sE=false;syo=10;mr=25} @RandomLocationsNearCaster{a=1;minr=5;maxradius=25;s=4}
  - projectile{onTick=SKILL_IGNIS-Tick;onHit=SKILL_IGNIS-Hit;onEnd=SKILL_IGNIS-End;v=6;i=1;hR=1;vR=1;hp=false;hnp=false;sfo=-8;sE=false;syo=10;mr=25} @RandomLocationsNearCaster{a=1;minr=5;maxradius=25;s=4}
  - projectile{onTick=SKILL_IGNIS-Tick;onHit=SKILL_IGNIS-Hit;onEnd=SKILL_IGNIS-End;v=6;i=1;hR=1;vR=1;hp=false;hnp=false;sfo=0;soffset=8;sE=false;syo=10;mr=25} @RandomLocationsNearCaster{a=1;minr=5;maxradius=25;s=4}
  - projectile{onTick=SKILL_IGNIS-Tick;onHit=SKILL_IGNIS-Hit;onEnd=SKILL_IGNIS-End;v=6;i=1;hR=1;vR=1;hp=false;hnp=false;sfo=0;soffset=-8;sE=false;syo=10;mr=25} @RandomLocationsNearCaster{a=1;minr=5;maxradius=25;s=4}
  - delay 20
  - projectile{onTick=SKILL_IGNIS-Tick;onHit=SKILL_IGNIS-Hit;onEnd=SKILL_IGNIS-End;v=6;i=1;hR=1;vR=1;hp=false;hnp=false;sfo=8;sE=false;syo=10;mr=25} @RandomLocationsNearCaster{a=1;minr=5;maxradius=25;s=4}
  - projectile{onTick=SKILL_IGNIS-Tick;onHit=SKILL_IGNIS-Hit;onEnd=SKILL_IGNIS-End;v=6;i=1;hR=1;vR=1;hp=false;hnp=false;sfo=-8;sE=false;syo=10;mr=25} @RandomLocationsNearCaster{a=1;minr=5;maxradius=25;s=4}
  - projectile{onTick=SKILL_IGNIS-Tick;onHit=SKILL_IGNIS-Hit;onEnd=SKILL_IGNIS-End;v=6;i=1;hR=1;vR=1;hp=false;hnp=false;sfo=0;soffset=8;sE=false;syo=10;mr=25} @RandomLocationsNearCaster{a=1;minr=5;maxradius=25;s=4}
  - projectile{onTick=SKILL_IGNIS-Tick;onHit=SKILL_IGNIS-Hit;onEnd=SKILL_IGNIS-End;v=6;i=1;hR=1;vR=1;hp=false;hnp=false;sfo=0;soffset=-8;sE=false;syo=10;mr=25} @RandomLocationsNearCaster{a=1;minr=5;maxradius=25;s=4}
  - delay 20
  - projectile{onTick=SKILL_IGNIS-Tick;onHit=SKILL_IGNIS-Hit;onEnd=SKILL_IGNIS-End;v=6;i=1;hR=1;vR=1;hp=false;hnp=false;sfo=8;sE=false;syo=10;mr=25} @RandomLocationsNearCaster{a=1;minr=5;maxradius=25;s=4}
  - projectile{onTick=SKILL_IGNIS-Tick;onHit=SKILL_IGNIS-Hit;onEnd=SKILL_IGNIS-End;v=6;i=1;hR=1;vR=1;hp=false;hnp=false;sfo=-8;sE=false;syo=10;mr=25} @RandomLocationsNearCaster{a=1;minr=5;maxradius=25;s=4}
  - projectile{onTick=SKILL_IGNIS-Tick;onHit=SKILL_IGNIS-Hit;onEnd=SKILL_IGNIS-End;v=6;i=1;hR=1;vR=1;hp=false;hnp=false;sfo=0;soffset=8;sE=false;syo=10;mr=25} @RandomLocationsNearCaster{a=1;minr=5;maxradius=25;s=4}
  - projectile{onTick=SKILL_IGNIS-Tick;onHit=SKILL_IGNIS-Hit;onEnd=SKILL_IGNIS-End;v=6;i=1;hR=1;vR=1;hp=false;hnp=false;sfo=0;soffset=-8;sE=false;syo=10;mr=25} @RandomLocationsNearCaster{a=1;minr=5;maxradius=25;s=4}
  - delay 20
  - setstance{stance=THARKON_NORMAL} @self

SKILL_IGNIS-Tick:
  Skills:
  - particles{particle=FLAME;amount=2;hS=0.1;vS=0.1;speed=0} @origin
  - particles{particle=SMOKE_NORMAL;amount=2;hS=0.1;vS=0.1;speed=0} @origin

SKILL_IGNIS-Hit:
  Skills:
  - particles{particle=SONIC_BOOM;amount=1;hS=0;vS=0;speed=0} @origin
  - particles{particle=EXPLOSION_NORMAL;amount=1;hS=0;vS=0;speed=0} @origin
  - sound{s=entity.lightning_bolt.impact;v=1;p=1} @origin
  - damage{a=10to20} @PIR{r=3}
  - throw{velocity=12;velocityY=4} @PIR{r=3}
  - ignite{ticks=100} @PIR{r=3} 0.5
  - delay 10
  - summon{type=THARKON_MINION;amount=1;radius=0} @origin

SKILL_IGNIS-End:
  Skills:
  - particles{particle=SONIC_BOOM;amount=1;hS=0;vS=0;speed=0} @origin
  - particles{particle=EXPLOSION_NORMAL;amount=1;hS=0;vS=0;speed=0} @origin
  - sound{s=entity.lightning_bolt.impact;v=1;p=1} @origin
  - damage{a=10to20} @PIR{r=3}
  - throw{velocity=12;velocityY=4} @PIR{r=3}
  - ignite{ticks=100} @PIR{r=3} 0.5
  - delay 10
  - summon{type=THARKON_MINION;amount=1;radius=0} @origin

IGNIS_EXPLODE_SIZE_1:
  Conditions:
  - playerwithin{d=2} true
  - size{s=<1} true
  Skills:
  - throw{velocity=4;velocityY=2} @PIR{r=3}
  - damage{a=5to8} @PIR{r=3}
  - fakeexplosion @self
  - delay 1
  - remove @self

IGNIS_EXPLODE_SIZE_2:
  Conditions:
  - playerwithin{d=2} true
  - size{s=>2} true
  Skills:
  - throw{velocity=15;velocityY=4} @PIR{r=3}
  - damage{a=12to20} @PIR{r=3}
  - ignite{ticks=200} @PIR{r=3} 0.5
  - fakeexplosion @self
  - delay 1
  - remove @self
#
#
#
#
#
#
#
#
#
#
# 𝑴𝑨𝑫𝑬 𝑩𝒀 𝑨𝑳𝑻𝑬𝑹𝑵𝑨𝑻𝑰𝑽𝑬𝑺𝑶𝑨𝑷
# 𝑫𝑰𝑺𝑪𝑶𝑹𝑫: https://discord.gg/eUFRvyzJua