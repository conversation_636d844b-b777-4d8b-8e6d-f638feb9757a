#
#███╗░░██╗██╗░░░██╗██╗░░██╗██╗████████╗██╗░░██╗░█████╗░██████╗░░█████╗░
#████╗░██║╚██╗░██╔╝╚██╗██╔╝╚═╝╚══██╔══╝██║░░██║██╔══██╗██╔══██╗██╔══██╗
#██╔██╗██║░╚████╔╝░░╚███╔╝░░░░░░░██║░░░███████║███████║██████╔╝███████║
#██║╚████║░░╚██╔╝░░░██╔██╗░░░░░░░██║░░░██╔══██║██╔══██║██╔══██╗██╔══██║
#██║░╚███║░░░██║░░░██╔╝╚██╗░░░░░░██║░░░██║░░██║██║░░██║██║░░██║██║░░██║
#╚═╝░░╚══╝░░░╚═╝░░░╚═╝░░╚═╝░░░░░░╚═╝░░░╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝╚═╝░░╚═╝
#
# VOID SOVEREIGN SUPPORT SKILLS - Created by vexy
#

# Player count based health scaling
NYXTHARA_PLAYER_COUNT:
  Skills:
  - sethealth{a=500} @self ~onSpawn
  - sethealth{a=750} @self ?hasaura{aura=NYXTHARA_2PLAYERS} ~onSpawn
  - sethealth{a=1000} @self ?hasaura{aura=NYXTHARA_3PLAYERS} ~onSpawn
  - sethealth{a=1250} @self ?hasaura{aura=NYXTHARA_4PLAYERS} ~onSpawn
  - sethealth{a=1500} @self ?hasaura{aura=NYXTHARA_5PLAYERS} ~onSpawn
  - sethealth{a=1750} @self ?hasaura{aura=NYXTHARA_6PLAYERS} ~onSpawn
  - sethealth{a=2000} @self ?hasaura{aura=NYXTHARA_7PLAYERS} ~onSpawn
  - sethealth{a=2250} @self ?hasaura{aura=NYXTHARA_8PLAYERS} ~onSpawn
  - sethealth{a=2500} @self ?hasaura{aura=NYXTHARA_9PLAYERS} ~onSpawn
  - sethealth{a=3000} @self ?hasaura{aura=NYXTHARA_10PLAYERS} ~onSpawn
  - aura{aura=NYXTHARA_2PLAYERS;d=999999;charges=1} @self ?playersInRadius{a=2;r=80}
  - aura{aura=NYXTHARA_3PLAYERS;d=999999;charges=1} @self ?playersInRadius{a=3;r=80}
  - aura{aura=NYXTHARA_4PLAYERS;d=999999;charges=1} @self ?playersInRadius{a=4;r=80}
  - aura{aura=NYXTHARA_5PLAYERS;d=999999;charges=1} @self ?playersInRadius{a=5;r=80}
  - aura{aura=NYXTHARA_6PLAYERS;d=999999;charges=1} @self ?playersInRadius{a=6;r=80}
  - aura{aura=NYXTHARA_7PLAYERS;d=999999;charges=1} @self ?playersInRadius{a=7;r=80}
  - aura{aura=NYXTHARA_8PLAYERS;d=999999;charges=1} @self ?playersInRadius{a=8;r=80}
  - aura{aura=NYXTHARA_9PLAYERS;d=999999;charges=1} @self ?playersInRadius{a=9;r=80}
  - aura{aura=NYXTHARA_10PLAYERS;d=999999;charges=1} @self ?playersInRadius{a=10;r=80}

# Spawn initialization
NYXTHARA_ONSPAWN:
  Skills:
  - message{m="&5&l⚡⚡⚡ NYX'THARA, THE VOID SOVEREIGN HAS AWAKENED! ⚡⚡⚡"} @PlayersInRadius{r=100}
  - message{m="&5The darkness stirs... reality trembles..."} @PlayersInRadius{r=100}
  - sound{s=entity.wither.spawn;v=1.0;p=0.3} @PlayersInRadius{r=100}
  - sound{s=entity.ender_dragon.growl;v=0.8;p=0.5} @PlayersInRadius{r=100}
  - effect:particles{p=portal;a=200;hS=8;vS=8;s=1.5} @self
  - effect:particles{p=explosion_large;a=5;hS=3;vS=3;s=0.5} @self
  - potion{type=DAMAGE_RESISTANCE;duration=200;level=2;force=true} @self
  - potion{type=FIRE_RESISTANCE;duration=999999;level=1;force=true} @self

# AI initialization on combat start
NYXTHARA_ONSPAWN_AI:
  Skills:
  - message{m="<mob.name>&f<&co>&r &5*The void sovereign focuses her malevolent gaze upon you*"} @PlayersInRadius{r=80}
  - sound{s=entity.enderman.scream;v=0.6;p=0.4} @PlayersInRadius{r=80}
  - effect:particles{p=portal;a=100;hS=5;vS=5;s=1.0} @self
  - potion{type=SPEED;duration=999999;level=1;force=true} @self
  - potion{type=JUMP;duration=999999;level=2;force=true} @self

# Random skill selector for varied combat
NYXTHARA_RANDOMSKILL_SELECTOR:
  Skills:
  - randomskill{
      skills=
      NYXTHARA_VOID_BARRAGE,
      NYXTHARA_SHADOW_STRIKE,
      NYXTHARA_DIMENSIONAL_SHIFT,
      NYXTHARA_VOID_PULSE,
      NYXTHARA_SHADOW_WAVE,
      NYXTHARA_VOID_GRASP,
      NYXTHARA_REALITY_DISTORTION
      }

# Additional combat abilities
NYXTHARA_VOID_BARRAGE:
  Cooldown: 8
  Skills:
  - message{m="<mob.name>&f<&co>&r &5*Void energy crackles around her form*"} @PlayersInRadius{r=50}
  - projectile{onTick=NYXTHARA_VOID_PROJECTILE-Tick;onHit=NYXTHARA_VOID_PROJECTILE-Hit;onEnd=NYXTHARA_VOID_PROJECTILE-End;v=14;i=1;hR=1;vR=1;tyo=0.6;sfo=0;sso=2;eso=0} @target
  - delay 6
  - projectile{onTick=NYXTHARA_VOID_PROJECTILE-Tick;onHit=NYXTHARA_VOID_PROJECTILE-Hit;onEnd=NYXTHARA_VOID_PROJECTILE-End;v=14;i=1;hR=1;vR=1;tyo=0.6;sfo=0;sso=-2;eso=0} @target
  - delay 6
  - projectile{onTick=NYXTHARA_VOID_PROJECTILE-Tick;onHit=NYXTHARA_VOID_PROJECTILE-Hit;onEnd=NYXTHARA_VOID_PROJECTILE-End;v=14;i=1;hR=1;vR=1;tyo=0.6;sfo=0;sso=0;eso=0} @target

NYXTHARA_SHADOW_STRIKE:
  Cooldown: 10
  Skills:
  - message{m="<mob.name>&f<&co>&r &5*Shadows coalesce into deadly spears*"} @PlayersInRadius{r=50}
  - effect:particles{p=smoke_large;a=30;hS=2;vS=2;s=0.5} @self
  - delay 15
  - damage{a=8to12} @target
  - throw{velocity=3;velocityY=1.2} @target
  - potion{type=BLINDNESS;duration=100;level=1;force=true} @target
  - effect:particles{p=smoke_large;a=20;hS=1.5;vS=1.5;s=0.4} @target

NYXTHARA_DIMENSIONAL_SHIFT:
  Cooldown: 12
  Skills:
  - message{m="<mob.name>&f<&co>&r &5*Reality warps around her*"} @PlayersInRadius{r=50}
  - effect:particles{p=portal;a=50;hS=3;vS=3;s=0.8} @self
  - sound{s=entity.enderman.teleport;v=0.7;p=0.5} @self
  - teleport{l=@RandomLocationsNearTargets{a=1;r=12;minr=5}} @self
  - delay 5
  - effect:particles{p=portal;a=30;hS=2;vS=2;s=0.5} @self
  - damage{a=6to10} @PlayersInRadius{r=4}

NYXTHARA_VOID_PULSE:
  Cooldown: 15
  Skills:
  - message{m="<mob.name>&f<&co>&r &5*A pulse of void energy emanates outward*"} @PlayersInRadius{r=50}
  - effect:particles{p=portal;a=80;hS=6;vS=2;s=0.8} @self
  - sound{s=entity.wither.shoot;v=0.6;p=0.6} @self
  - delay 10
  - damage{a=4to7} @PlayersInRadius{r=8}
  - damage{a=2to5} @PlayersInRadius{r=15}
  - potion{type=SLOWNESS;duration=80;level=1;force=true} @PlayersInRadius{r=8}
  - throw{velocity=2;velocityY=0.8} @PlayersInRadius{r=8}

NYXTHARA_SHADOW_WAVE:
  Cooldown: 18
  Skills:
  - message{m="<mob.name>&f<&co>&r &5*Darkness spreads like a tide*"} @PlayersInRadius{r=50}
  - effect:particles{p=smoke_large;a=100;hS=8;vS=1;s=0.6} @self
  - sound{s=entity.wither.ambient;v=0.5;p=0.4} @self
  - delay 20
  - damage{a=5to8} @PlayersInRadius{r=12}
  - potion{type=HUNGER;duration=120;level=2;force=true} @PlayersInRadius{r=12}
  - potion{type=WEAKNESS;duration=100;level=1;force=true} @PlayersInRadius{r=12}

NYXTHARA_VOID_GRASP:
  Cooldown: 14
  Skills:
  - message{m="<mob.name>&f<&co>&r &5*Tendrils of void energy reach out*"} @PlayersInRadius{r=50}
  - effect:particles{p=portal;a=40;hS=4;vS=4;s=0.7} @target
  - sound{s=entity.enderman.hurt;v=0.5;p=0.7} @target
  - delay 8
  - damage{a=7to11} @target
  - teleport{l=@self;yoffset=0;xoffset=2;zoffset=2} @target
  - potion{type=LEVITATION;duration=60;level=1;force=true} @target

NYXTHARA_REALITY_DISTORTION:
  Cooldown: 20
  Skills:
  - message{m="<mob.name>&f<&co>&r &5*Reality bends and twists unnaturally*"} @PlayersInRadius{r=50}
  - effect:particles{p=portal;a=120;hS=6;vS=6;s=1.2} @self
  - sound{s=block.portal.ambient;v=0.8;p=0.3} @self
  - delay 25
  - teleport{l=@RandomLocationsNearTargets{a=1;r=15;minr=3}} @PlayersInRadius{r=20}
  - damage{a=6to9} @PlayersInRadius{r=20}
  - potion{type=CONFUSION;duration=150;level=1;force=true} @PlayersInRadius{r=20}

# Victory/Death skill
NYXTHARA_VICTORY:
  Skills:
  - message{m="&5&l⚡⚡⚡ THE VOID SOVEREIGN HAS BEEN VANQUISHED! ⚡⚡⚡"} @PlayersInRadius{r=100}
  - message{m="&5*The darkness recedes... for now...*"} @PlayersInRadius{r=100}
  - sound{s=entity.wither.death;v=1.0;p=0.4} @PlayersInRadius{r=100}
  - sound{s=entity.ender_dragon.death;v=0.8;p=0.6} @PlayersInRadius{r=100}
  - effect:particles{p=portal;a=300;hS=10;vS=10;s=2.0} @self
  - effect:particles{p=explosion_large;a=10;hS=5;vS=5;s=1.0} @self
  - delay 40
  - message{m="&5&l⟨ LEGENDARY REWARDS AWAIT THE VICTORIOUS ⟩"} @PlayersInRadius{r=100}
#
#
#
#
#
#
#
#
#
#
# 𝑪𝑹𝑬𝑨𝑻𝑬𝑫 𝑩𝒀 𝑽𝑬𝑿𝒀
# 𝑩𝑨𝑺𝑬𝑫 𝑶𝑵 𝑻𝑯𝑨𝑹𝑲𝑶𝑵 𝑩𝒀 𝑨𝑳𝑻𝑬𝑹𝑵𝑨𝑻𝑰𝑽𝑬𝑺𝑶𝑨𝑖
